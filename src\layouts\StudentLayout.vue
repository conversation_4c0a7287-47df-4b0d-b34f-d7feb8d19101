<template>
  <div class="student-layout">
    <!-- 移动端遮罩 -->
    <div
      v-if="isMobile() && sidebarVisible"
      class="sidebar-overlay"
      @click="closeSidebar"
    ></div>

    <!-- 侧边栏 -->
    <aside
      class="sidebar"
      :class="{
        'sidebar-collapsed': sidebarCollapsed,
        'sidebar-mobile': isMobile(),
        'sidebar-visible': sidebarVisible
      }"
    >
      <div class="sidebar-header">
        <div class="logo">
          <svg class="logo-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
          </svg>
          <span v-if="!sidebarCollapsed" class="logo-text">学习平台</span>
        </div>
        <button
          class="sidebar-toggle"
          @click="toggleSidebar"
          :title="sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'"
        >
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 12H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <path d="M3 6H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <path d="M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </button>
      </div>

      <nav class="sidebar-nav">
        <ul class="nav-list">
          <li class="nav-item">
            <router-link to="/student/dashboard" class="nav-link" @click="handleNavClick">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span v-if="!sidebarCollapsed || isMobile()" class="nav-text">学习概览</span>
            </router-link>
          </li>

          <li class="nav-item">
            <router-link to="/student/courses" class="nav-link" @click="handleNavClick">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M2 3H8C9.06087 3 10.0783 3.42143 10.8284 4.17157C11.5786 4.92172 12 5.93913 12 7V21C12 20.2044 11.6839 19.4413 11.1213 18.8787C10.5587 18.3161 9.79565 18 9 18H2V3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M22 3H16C14.9391 3 13.9217 3.42143 13.1716 4.17157C12.4214 4.92172 12 5.93913 12 7V21C12 20.2044 12.3161 19.4413 12.8787 18.8787C13.4413 18.3161 14.2044 18 15 18H22V3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span v-if="!sidebarCollapsed || isMobile()" class="nav-text">我的课程</span>
            </router-link>
          </li>

          <li class="nav-item">
            <router-link to="/student/knowledge-graph" class="nav-link" @click="handleNavClick">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                <circle cx="12" cy="3" r="1" stroke="currentColor" stroke-width="2"/>
                <circle cx="12" cy="21" r="1" stroke="currentColor" stroke-width="2"/>
                <circle cx="3" cy="12" r="1" stroke="currentColor" stroke-width="2"/>
                <circle cx="21" cy="12" r="1" stroke="currentColor" stroke-width="2"/>
                <path d="M12 9V3" stroke="currentColor" stroke-width="2"/>
                <path d="M12 21V15" stroke="currentColor" stroke-width="2"/>
                <path d="M15 12H21" stroke="currentColor" stroke-width="2"/>
                <path d="M3 12H9" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span v-if="!sidebarCollapsed || isMobile()" class="nav-text">知识图谱</span>
            </router-link>
          </li>

          <li class="nav-item">
            <router-link to="/student/progress" class="nav-link" @click="handleNavClick">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 12H18L15 21L9 3L6 12H2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span v-if="!sidebarCollapsed || isMobile()" class="nav-text">学习进度</span>
            </router-link>
          </li>

          <li class="nav-item">
            <router-link to="/student/materials" class="nav-link" @click="handleNavClick">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V9L13 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M13 2V9H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span v-if="!sidebarCollapsed || isMobile()" class="nav-text">学习资料</span>
            </router-link>
          </li>

          <li class="nav-item">
            <router-link to="/student/my" class="nav-link" @click="handleNavClick">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
              </svg>
              <span v-if="!sidebarCollapsed || isMobile()" class="nav-text">个人中心</span>
            </router-link>
          </li>
        </ul>
      </nav>

      <div class="sidebar-footer">
        <div class="user-info" v-if="!sidebarCollapsed">
          <div class="user-avatar">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="user-details">
            <div class="user-name">{{ currentUser.name || currentUser.username || '学生' }}</div>
            <div class="user-role">学生</div>
          </div>
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 顶部导航栏 -->
      <header class="top-header">
        <div class="header-left">
          <button
            v-if="isMobile()"
            class="mobile-menu-toggle"
            @click="toggleSidebar"
            :title="sidebarVisible ? '关闭菜单' : '打开菜单'"
          >
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 12H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M3 6H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M3 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
          <h1 class="page-title">{{ pageTitle }}</h1>
        </div>
        <div class="header-right">
          <button
            class="theme-toggle"
            @click="toggleTheme"
            :title="isDark() ? '切换到亮色模式' : '切换到暗色模式'"
          >
            <svg v-if="isDark()" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="5" stroke="currentColor" stroke-width="2"/>
              <path d="M12 1V3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M12 21V23" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M4.22 4.22L5.64 5.64" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M18.36 18.36L19.78 19.78" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M1 12H3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M21 12H23" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M4.22 19.78L5.64 18.36" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M18.36 5.64L19.78 4.22" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
            <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 12.79C20.8427 14.4922 20.2039 16.1144 19.1583 17.4668C18.1127 18.8192 16.7035 19.8458 15.0957 20.4265C13.4879 21.0073 11.7473 21.1181 10.0713 20.746C8.39524 20.3739 6.84947 19.5345 5.62596 18.3259C4.40245 17.1174 3.54475 15.5884 3.14823 13.9155C2.75171 12.2427 2.83421 10.4995 3.38681 8.88482C3.93941 7.27015 4.94043 5.84482 6.27133 4.78218C7.60223 3.71954 9.20819 3.05999 10.9 2.88C9.2019 4.91127 8.66697 7.51565 9.45273 9.91716C10.2385 12.3187 12.2498 14.2054 14.7304 14.8284C17.2109 15.4514 19.8335 14.7358 21.75 12.9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
      </header>

      <!-- 页面内容 -->
      <div class="page-content">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useSidebarResponsive, useTheme } from '@/utils/responsive'
import { getCurrentUser } from '@/api/auth'

export default {
  name: 'StudentLayout',
  setup() {
    const route = useRoute()
    const { sidebarCollapsed, sidebarVisible, toggleSidebar, closeSidebar, isMobile } = useSidebarResponsive()
    const { theme, toggleTheme, isDark } = useTheme()
    
    // 获取当前用户信息
    const currentUser = ref(getCurrentUser() || {})

    const pageTitle = computed(() => {
      const titles = {
        '/student/dashboard': '学习概览',
        '/student/courses': '我的课程',
        '/student/knowledge-graph': '知识图谱',
        '/student/progress': '学习进度',
        '/student/materials': '学习资料',
        '/student/my': '个人中心'
      }
      return titles[route.path] || '学习平台'
    })

    const handleNavClick = () => {
      // 移动端点击导航后关闭侧边栏
      if (isMobile()) {
        closeSidebar()
      }
    }

    return {
      sidebarCollapsed,
      sidebarVisible,
      isDark,
      pageTitle,
      currentUser,
      toggleSidebar,
      toggleTheme,
      closeSidebar,
      isMobile,
      handleNavClick
    }
  }
}
</script>

<style scoped>
.student-layout {
  display: flex;
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.sidebar {
  width: 280px;
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  transition: width var(--transition-normal);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  z-index: var(--z-fixed);
}

.sidebar-collapsed {
  width: 80px;
}

.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo-icon {
  width: 32px;
  height: 32px;
  color: var(--primary-color);
}

.logo-text {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  white-space: nowrap;
}

.sidebar-toggle {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.sidebar-toggle svg {
  width: 20px;
  height: 20px;
}

.sidebar-nav {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: var(--spacing-xs);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
}

.nav-link:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.nav-link.router-link-active {
  background-color: var(--primary-100);
  color: var(--primary-600);
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-text {
  white-space: nowrap;
  overflow: hidden;
}

.sidebar-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-100);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-600);
}

.user-avatar svg {
  width: 20px;
  height: 20px;
}

.user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.user-role {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.main-content {
  flex: 1;
  margin-left: 280px;
  transition: margin-left var(--transition-normal);
  display: flex;
  flex-direction: column;
}

.sidebar-collapsed + .main-content {
  margin-left: 80px;
}

.top-header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md) var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.mobile-menu-toggle {
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  display: none;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.mobile-menu-toggle:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.mobile-menu-toggle svg {
  width: 20px;
  height: 20px;
}

.page-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.theme-toggle {
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.theme-toggle:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.theme-toggle svg {
  width: 20px;
  height: 20px;
}

.page-content {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
}

/* 移动端遮罩 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: calc(var(--z-fixed) - 1);
  opacity: 0;
  animation: fadeIn var(--transition-normal) forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* 移动端侧边栏 */
.sidebar-mobile {
  transform: translateX(-100%);
  transition: transform var(--transition-normal);
  z-index: var(--z-modal);
}

.sidebar-mobile.sidebar-visible {
  transform: translateX(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .page-content {
    padding: var(--spacing-md);
  }

  .top-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .page-title {
    font-size: var(--text-lg);
  }
}

@media (max-width: 480px) {
  .page-content {
    padding: var(--spacing-sm);
  }

  .top-header {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .sidebar-header {
    padding: var(--spacing-md);
  }

  .sidebar-nav {
    padding: var(--spacing-sm);
  }

  .sidebar-footer {
    padding: var(--spacing-md);
  }
}
</style>
