<template>
  <div :class="['graph-page', { embedded: isEmbedded }]">
          <div v-if="!isEmbedded" class="graph-header">
      <div class="course-selector" v-if="!courseId">
        <div class="course-list">
          <div v-if="isLoading" class="loading">加载课程列表中...</div>
          <div v-else-if="courses.length === 0" class="no-courses">暂无课程</div>
          <div v-else class="course-cards">
            <div 
              v-for="course in courses" 
              :key="course.id" 
              class="course-card"
              @click="navigateToCourseGraph(course.id)"
            >
              <h3>{{ course.name }}</h3>
              <p>{{ course.content }}</p>
              <div class="course-meta">
                <span class="node-count">{{ course.nodeCount || 0 }} 个知识点</span>
                <span class="view-graph">查看图谱</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="course-graph-header" v-else>
        <button class="nav-btn my-btn" @click="goToMyPage">返回图谱列表</button>
        <h1>{{ currentCourse ? currentCourse.name : '课程' }}知识图谱</h1>
        <button class="nav-btn outline-btn" @click="goToOutline">进入大纲编辑</button>
      </div>
    </div>
    
    <div class="graph-container" ref="graphContainer" v-if="courseId">
      <div v-if="isGraphLoading" class="graph-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载知识图谱中...</div>
      </div>
      <RelationGraph 
        ref="graphRef" 
        :options="graphOptions"
        :layoutName="currentLayout"
        :on-node-click="handleNodeClick"
        :on-line-click="handleLineClick"
        @node-mouseenter="handleNodeHover"
        @node-mouseleave="() => hoveredNode = null"
      />
      
      <div class="node-tooltip" v-if="hoveredNode" :style="tooltipStyle">
        <div class="tooltip-title">{{ hoveredNode.text }}</div>
        <div class="tooltip-content" v-if="hoveredNode.content">
          {{ hoveredNode.content.substring(0, 100) }}{{ hoveredNode.content.length > 100 ? '...' : '' }}
        </div>
        <div class="tooltip-category" v-if="hoveredNode.category">
          类别: <span>{{ getCategoryName(hoveredNode.category) }}</span>
        </div>
      </div>
      
      <node-detail-panel
        v-if="selectedNode"
        :key="selectedNode.id"
        :node="selectedNode"
        :relatedNodes="relatedNodes"
        @save="saveNodeChanges"
        @cancel="toggleEditing"
        @close="closeNodeDetail"
        @select-related="selectRelatedNode"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, reactive, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import RelationGraph from 'relation-graph-vue3';
import type { RGJsonData, RGNode, RGLine, RGLink, RGUserEvent, RGOptions, RelationGraphComponent, JsonNode } from 'relation-graph-vue3';
import { getCourseList, getCourseGraph, getNodeLines, getNodeStyle ,updateNode as updateNodeApi} from '../api/courses';
import NodeDetailPanel from '../components/graph/NodeDetailPanel.vue';

// 定义类型接口
interface Course {
  id: string | number;
  name: string;
  content?: string;
  nodeCount?: number;
}

interface Node {
  id: string | number;
  text: string;
  content?: string;
  category?: string;
  data?: any;
}

interface NodeStyle {
  type?: string;
  nodeShape?: number;
  nodeWidth?: number;
  nodeHeight?: number;
  borderWidth?: number;
  borderHeight?: number;
  color?: string;
  fontColor?: string;
}

// 路由相关
const route = useRoute();
const router = useRouter();
// 支持通过路径参数或查询参数获取课程 ID
const courseId = computed(() => route.params.id || route.query.courseId);

// 判断是否作为嵌入视图（iframe）加载
const isEmbedded = computed(() => 'embedded' in route.query);

// 课程列表相关
const courses = ref<Course[]>([]);
const isLoading = ref(false);
const currentCourse = ref<Course | null>(null);

// 图谱相关
const graphRef = ref<RelationGraphComponent>();
const graphContainer = ref<HTMLElement | null>(null);
const isGraphLoading = ref(false);
const selectedNode = ref<Node | null>(null);
const hoveredNode = ref<Node | null>(null);
const tooltipStyle = reactive({
  left: '0px',
  top: '0px'
});

// 编辑相关
const isEditing = ref(true);
const editedContent = ref('');
const editedTitle = ref('');
const isSearching = ref(false);

// 当前布局
const currentLayout = ref('center');

// 关联节点
const relatedNodes = ref<Node[]>([]);

// 知识点分类名称映射
const categoryNames = {
  'root': '根节点',
  'concept': '概念',
  'principle': '原理',
  'method': '方法',
  'tool': '工具',
  'application': '应用',
  'branch': '分支',
  'history': '历史',
  'structure': '结构',
  'analysis': '分析',
  'property': '性质',
  'material': '材料'
};

// 获取分类名称
const getCategoryName = (category: string): string => {
  return categoryNames[category as keyof typeof categoryNames] || category;
};



// 加载课程列表
const loadCourses = async () => {
  isLoading.value = true;
  try {
    const data = await getCourseList();
    courses.value = data.rows || [];
    console.log('tupu列表:', courses.value);
  } catch (error) {
    console.error('加载课程列表失败:', error);
    courses.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 加载当前课程信息
const loadCurrentCourse = async () => {
  await loadCourses();
  if (courseId.value && courses.value.length > 0) {
    // 查找当前课程信息
    const course = courses.value.find(c => c.id.toString() === courseId.value.toString());
    if (course) {
      currentCourse.value = course;
    }
  }
};

// 加载图谱数据
const loadGraphData = async () => {
  if (!courseId.value) return;
  
  isGraphLoading.value = true;
  try {
    // 1. 获取节点数据
    const nodes = await getCourseGraph(courseId.value);
    
    if (!nodes || nodes.length === 0) {
      console.error('未获取到节点数据');
      isGraphLoading.value = false;
      return;
    }
    
    // 2. 获取所有节点的样式
    const nodePromises = nodes.map(async (node: any) => {
      try {
        const style = await getNodeStyle(node.id);
        return {
          id: node.id.toString(),
          text: node.name,
         
          data:{
            content: node.content,
          },
          type: style?.type,
          nodeShape: style?.nodeShape,
          width: style?.nodeWidth,
          height: style?.nodeHeight,
          borderWidth: style?.borderWidth,
          borderHeight: style?.borderHeight,
        };
      } catch (error) {
        console.error(`获取节点 ${node.id} 样式失败:`, error);
        return {
          id: node.id.toString(),
          text: node.name,
          content: node.content
        };
      }
    });
    
    const processedNodes = await Promise.all(nodePromises);
    
    // 3. 获取所有节点的连线
    const linePromises = nodes.map((node: any) => getNodeLines(node.id));
    const linesArrays = await Promise.all(linePromises);
    
    // 扁平化连线数组
    const allLines = linesArrays.flat();
    
    const processedLines = allLines.map((line: any) => ({
      from: line.nodeId.toString(),
      to: line.targetId.toString(),
      text: line.content
    }));
    
    // 4. 构建图谱数据
    const graphData = {
      rootId: nodes[0].id.toString(),
      nodes: processedNodes,
      links: processedLines
    };
    
    // 5. 渲染图谱
    renderGraph(graphData);
    
  } catch (error) {
    console.error('加载图谱数据失败:', error);
    // 可以在这里添加fallback逻辑，例如加载模拟数据
  } finally {
    isGraphLoading.value = false;
  }
};

// 渲染图谱数据
const renderGraph = async (graphData: any) => {
  try {
    // 获取图谱实例
    const graphInstance = graphRef.value?.getInstance();
    if (!graphInstance) {
      console.error('图谱实例未初始化');
      return;
    }
    
    // 设置当前布局
    graphInstance.options.layoutName = currentLayout.value;
    
    // 加载数据
    await graphInstance.setJsonData(graphData);
    
    // 居中和适配视图
    await graphInstance.moveToCenter();
    await graphInstance.zoomToFit();
    
    console.log(`图谱加载完成，使用布局: ${currentLayout.value}`);
  } catch (error) {
    console.error('渲染图谱失败:', error);
  }
};

// 切换布局
const switchLayout = (layout: string) => {
  if (currentLayout.value === layout) return;
  
  console.log(`切换布局到: ${layout}`);
  currentLayout.value = layout;
  
  const graphInstance = graphRef.value?.getInstance();
  if (graphInstance) {
    try {
      // 保存当前数据
      const currentData = graphInstance.getJsonData();
      
      // 更新布局选项
      (graphOptions as any).layoutName = layout;
      
      // 使用新布局重新加载数据
      isGraphLoading.value = true;
      
      // 延迟执行以确保UI更新
      setTimeout(async () => {
        try {
          // 重新设置数据
          await graphInstance.setJsonData(currentData);
          await graphInstance.moveToCenter();
          await graphInstance.zoomToFit();
          console.log(`布局已切换到: ${layout}`);
        } catch (error) {
          console.error('重新加载数据失败:', error);
        } finally {
          isGraphLoading.value = false;
        }
      }, 100);
    } catch (error) {
      console.error('切换布局失败:', error);
      isGraphLoading.value = false;
    }
  }
};

// 处理节点点击
const handleNodeClick = (node: RGNode, $event: RGUserEvent) => {
  console.log('Node clicked:', node);
  
  // 创建一个简化的数据结构，确保直接提供必要的属性
  selectedNode.value = {
    id: node.id,
    name: node.text || '',
    content: node.data.content || ''
  };
  
  console.log('Selected node data:', selectedNode.value);
  
  // 更新编辑值
  editedContent.value = node.data.content || '';
  editedTitle.value = node.text || '';
  isEditing.value = true; // 直接进入编辑模式
  
  // 获取关联节点
  const graphInstance = graphRef.value?.getInstance();
  if (graphInstance) {
    // 使用getLinesByNode代替getRelationsByNodeId
    const relations = graphInstance.getLinesByNode(node.id);
    relatedNodes.value = relations.map(rel => {
      // 确定相关节点ID
      const relatedNodeId = rel.fromNodeId === node.id ? rel.toNodeId : rel.fromNodeId;
      const relatedNode = graphInstance.getNodeById(relatedNodeId);
      
      if (!relatedNode) {
        console.error('找不到关联节点:', relatedNodeId);
        return null;
      }
      
      return {
        id: relatedNodeId,
        name: relatedNode.text || '',
        content: relatedNode.data.content || '',
        relationType: rel.text || '关联',
        category: relatedNode.data?.category || '',
      };
    }).filter(Boolean) as Node[]; // 过滤掉null值
    
    console.log('关联节点:', relatedNodes.value);
  }
};

// 处理线条点击
const handleLineClick = (lineObject: RGLine, linkObject: RGLink, $event: RGUserEvent) => {
  console.log('线条点击:', lineObject);
};

// 处理节点悬停
const handleNodeHover = (node: any, event: MouseEvent) => {
  if (node) {
    hoveredNode.value = node.data;
    
    // 计算提示框位置
    const rect = event.target?.getBoundingClientRect();
    const containerRect = graphContainer.value?.getBoundingClientRect();
    
    if (rect && containerRect) {
      tooltipStyle.left = `${rect.left - containerRect.left + rect.width / 2}px`;
      tooltipStyle.top = `${rect.top - containerRect.top - 10}px`;
    }
  } else {
    hoveredNode.value = null;
  }
};

// 切换编辑模式/取消编辑
const toggleEditing = () => {
  // 从编辑模式切换到查看模式（取消操作）
  if (isEditing.value) {
    // 还原为原始值
    editedContent.value = selectedNode.value?.content || '';
    editedTitle.value = selectedNode.value?.text || '';
    // 关闭面板
    closeNodeDetail();
  }
  // 切换编辑状态
  isEditing.value = !isEditing.value;
};

// 保存节点内容变更
 const saveNodeChanges = async (updatedNode: any) => {
  if (!selectedNode.value) return;
  
  // 更新节点内容
  if (selectedNode.value) {
    selectedNode.value.content = updatedNode.content;
    selectedNode.value.name = updatedNode.text;
  }


  console.log('节点内容变更', selectedNode.value);
  
  // 更新图谱中的节点数据
  const graphInstance = graphRef.value?.getInstance();
  if (graphInstance && selectedNode.value) {
    const node = graphInstance.getNodeById(selectedNode.value.id);
    if (node) {
      // 更新节点数据
      node.data.content = updatedNode.content;
      node.data.text = updatedNode.text;
      
      // 更新节点文本显示
      node.text = updatedNode.text;
      
      // 直接更新DOM中的节点文本
      const nodeElement = document.querySelector(`.rel-node[node-id="${selectedNode.value.id}"] .c-node-text`);
      if (nodeElement) {
        nodeElement.textContent = updatedNode.text;
      }
      
      // 更新悬停节点信息
      if (hoveredNode.value && hoveredNode.value.id === selectedNode.value.id) {
        hoveredNode.value.text = updatedNode.text;
        hoveredNode.value.content = updatedNode.content;
      }
      
      // 这里可以添加API调用，将更新后的内容保存到后端
      console.log('保存节点内容:', selectedNode.value.id, updatedNode.text, updatedNode.content);

     await updateNodeApi({
        id: selectedNode.value.id,
        name: updatedNode.text,
        content: updatedNode.content
      });  
      
      // 刷新图谱
      graphInstance.refresh();
    }
  }
  
  // 退出编辑模式
  isEditing.value = false;
};

// 关闭节点详情面板
const closeNodeDetail = () => {
  selectedNode.value = null;
  relatedNodes.value = [];
  isEditing.value = false;
};

// 选择关联节点
const selectRelatedNode = (node: Node) => {
  console.log('选择关联节点:', node);
  
  const graphInstance = graphRef.value?.getInstance();
  if (graphInstance) {
    const targetNode = graphInstance.getNodeById(node.id);
    if (targetNode) {
      // 聚焦到节点
      graphInstance.focusNodeById(node.id);
      
      // 简单设置选中节点数据，保持结构一致性
      selectedNode.value = {
        id: node.id,
        name: node.name || '',
        content: node.content || ''
      };
      
      console.log('已设置选中节点:', selectedNode.value);
    }
  }
};

// 缩放控制
const zoomIn = () => {
  const graphInstance = graphRef.value?.getInstance();
  if (graphInstance) {
    graphInstance.setZoom(graphInstance.getZoom() * 1.2);
  }
};

const zoomOut = () => {
  const graphInstance = graphRef.value?.getInstance();
  if (graphInstance) {
    graphInstance.setZoom(graphInstance.getZoom() / 1.2);
  }
};

const resetView = () => {
  const graphInstance = graphRef.value?.getInstance();
  if (graphInstance) {
    graphInstance.moveToCenter();
    graphInstance.zoomToFit();
  }
};

// 导航到课程图谱
const navigateToCourseGraph = (id: string | number) => {
  router.push(`/graph/${id}`);
};

// 返回课程列表
const goBack = () => {
  router.push('/graph');
};

// 导航到知识大纲
const goToOutline = () => {
  if (courseId.value) {
    router.push(`/outline/${courseId.value}`);
  } else {
    router.push('/outline');
  }
};

// 导航到首页
const goToHome = () => {
  router.push('/');
};

// 导航到个人中心的知识图谱列表
const goToMyPage = () => {
  router.push('/my?tab=graphview');
};


// AI搜索知识点内容
const searchAIContent = async () => {
  if (!editedTitle.value.trim()) {
    alert('请先输入知识点标题');
    return;
  }
  
  isSearching.value = true;
  
  try {
    // 使用AI API进行搜索
    const apiKey = 'sk-Cvdoz7cRodGwhmMmioj1VrZOhCKNzBpoHkaVKAmqL7qLaQ3B';
    const apiUrl = 'https://api.openai.com/v1/chat/completions';
    
    const prompt = `请简要介绍"${editedTitle.value}"这个知识点的相关内容，包括定义、特点、应用等方面，限制在300字以内。`;
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个知识图谱辅助工具，擅长简洁地解释各个知识点'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 500
      })
    });
    
    const data = await response.json();
    
    if (data && data.choices && data.choices[0] && data.choices[0].message) {
      const content = data.choices[0].message.content.trim();
      
      if (content) {
        // 添加标题和内容
        const formattedContent = `${editedTitle.value}：\n\n${content}`;
        editedContent.value = formattedContent;
      } else {
        alert('AI未生成相关内容');
        await searchUsingBackupMethod();
      }
    } else {
      console.error('AI API响应格式异常:', data);
      await searchUsingBackupMethod();
    }
  } catch (error) {
    console.error('AI搜索失败:', error);
    // 尝试备用方法
    await searchUsingBackupMethod();
  } finally {
    isSearching.value = false;
  }
};

// 备用搜索方法
const searchUsingBackupMethod = async () => {
  try {
    // 改进的内容生成
    const keywords = editedTitle.value.split(/\s+/).filter(k => k.length > 1);
    
    // 根据知识点类型提供更具体的内容
    let typeSpecificContent = '';
    
    if (editedTitle.value.includes('测量') || editedTitle.value.includes('测绘')) {
      typeSpecificContent = '这是土木工程测量中的重要概念，涉及到工程定位、放样和验收等环节。';
    } else if (editedTitle.value.includes('结构') || editedTitle.value.includes('力学')) {
      typeSpecificContent = '这是结构工程中的核心概念，与建筑物的稳定性、受力分析和抗震设计等密切相关。';
    } else if (editedTitle.value.includes('材料') || editedTitle.value.includes('混凝土') || editedTitle.value.includes('钢筋')) {
      typeSpecificContent = '这与建筑材料的性能、应用和检测有关，对工程质量和耐久性有重要影响。';
    } else if (editedTitle.value.includes('设计') || editedTitle.value.includes('施工')) {
      typeSpecificContent = '这与工程设计和施工过程密切相关，涉及到规范要求、技术标准和实践经验。';
    }
    
    // 生成通用介绍
    const baseInfo = `${editedTitle.value}是土木工程领域中的一个重要知识点`;
    const relationInfo = keywords.length > 0 ? 
      `，与${keywords.join('、')}等概念有密切关联` : 
      '，是理解相关工程原理的基础';
    
    // 补充应用信息
    const applicationInfo = typeSpecificContent || 
      `它在工程实践中有广泛应用，特别是在设计、施工和验收环节。掌握这一知识点对提高工程质量和效率有重要意义。`;
    
    // 组合内容
    editedContent.value = `${baseInfo}${relationInfo}。\n\n${applicationInfo}\n\n更多详细信息建议参考专业教材和规范标准。`;
  } catch (error) {
    console.error('备用搜索失败:', error);
    alert('内容生成失败，请手动输入内容');
  }
};

// 监听路由变化
watch(courseId, async (newId) => {
  if (newId) {
    await loadCurrentCourse();
    loadGraphData();
  }
}, { immediate: true });

// 生命周期钩子
onMounted(async () => {
  console.log('GraphPage mounted');
  
  if (courseId.value) {
    await loadCurrentCourse();
    // 延迟加载图谱数据，确保组件已完全渲染
    setTimeout(() => {
      loadGraphData();
    }, 500);
  }
});

// 图谱配置
const graphOptions: RGOptions = {
  debug: false,
  allowSwitchLineShape: true,
  allowSwitchJunctionPoint: true,
  defaultNodeColor: 'rgba(66, 153, 225, 0.8)',
  defaultNodeBorderWidth: 1,
  defaultNodeBorderColor: 'rgba(66, 153, 225, 1)',
  defaultLineColor: 'rgba(128, 128, 128, 0.5)',
  defaultLineWidth: 2,
  defaultLineShape: 1,
  defaultJunctionPoint: 'border',
  layoutName: 'center', // 默认布局
  layouts: [
    {
      layoutName: 'center',
      layoutClassName: 'RelationGraphCenterLayout',
      distance: 200
    } as any,
    {
      layoutName: 'force',
      layoutClassName: 'RelationGraphForceLayout',
      options: {
        strength: -1600,
        distance: 200,
        nodeStrength: -800,
        collide: 100
      }
    } as any
  ],
  // 节点样式配置
  nodeStyle: {
    strokeWidth: 2,
    strokeColor: '#666',
    fillColor: '#fff',
    textColor: '#333',
    fontSize: 14,
    lineHeight: 20,
    borderRadius: 5,
    padding: 10
  },
  // 不同类型节点的样式
  nodeStyles: {
    root: {
      strokeColor: '#4299e1',
      fillColor: 'rgba(66, 153, 225, 0.1)',
      textColor: '#2b6cb0',
      fontSize: 16,
      fontWeight: 'bold'
    },
    concept: {
      strokeColor: '#38a169',
      fillColor: 'rgba(56, 161, 105, 0.1)',
      textColor: '#276749'
    },
    principle: {
      strokeColor: '#805ad5',
      fillColor: 'rgba(128, 90, 213, 0.1)',
      textColor: '#553c9a'
    },
    method: {
      strokeColor: '#d69e2e',
      fillColor: 'rgba(214, 158, 46, 0.1)',
      textColor: '#975a16'
    },
    tool: {
      strokeColor: '#dd6b20',
      fillColor: 'rgba(221, 107, 32, 0.1)',
      textColor: '#9c4221'
    },
    application: {
      strokeColor: '#3182ce',
      fillColor: 'rgba(49, 130, 206, 0.1)',
      textColor: '#2c5282'
    }
  }
};
</script>

<style scoped>
.graph-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 20px;
}

.graph-page.embedded {
  padding: 0;
}

.graph-header {
  margin-bottom: 20px;
}

.course-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.course-selector h1,
.course-graph-header h1 {
  margin-bottom: 20px;
  font-size: 24px;
  color: var(--text-color);
}

.course-selector h1 {
  margin-bottom: 0;
}

.course-list {
  margin-top: 20px;
}

.loading, .no-courses {
  padding: 20px;
  text-align: center;
  color: var(--text-lighter);
}

.course-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.course-card {
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-md);
  padding: 20px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.3s ease;
}

.course-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.course-card h3 {
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--primary-color);
}

.course-card p {
  margin-bottom: 15px;
  color: var(--text-color);
  font-size: 14px;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.node-count {
  color: var(--text-lighter);
}

.view-graph {
  color: var(--primary-color);
  font-weight: 500;
}

.course-graph-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.nav-btn {
  padding: 8px 12px;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  cursor: pointer;
  background-color: var(--background-color-light);
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.nav-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.navigation-buttons {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
  width: 100%;
  justify-content: center;
}

.back-button, .nav-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--primary-color);
  font-weight: 500;
  padding: 5px 10px;
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease;
}

.back-button:hover, .nav-button:hover {
  background-color: rgba(var(--primary-color-rgb), 0.1);
  transform: translateY(-2px);
}

.back-icon, .nav-icon {
  margin-right: 5px;
  font-size: 18px;
}

.graph-controls {
  display: flex;
  gap: 10px;
}

.control-button {
  background-color: var(--background-color-light);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: 5px 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-button:hover {
  background-color: var(--primary-color);
  color: white;
}

.graph-container {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  position: relative;
  background-color: var(--background-color-light);
}

.graph-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(var(--background-color-rgb), 0.8);
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(var(--primary-color-rgb), 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-color);
}

.node-tooltip {
  position: absolute;
  background-color: var(--background-color-light);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: 10px;
  box-shadow: var(--shadow-md);
  max-width: 250px;
  transform: translate(-50%, -100%);
  z-index: 100;
  pointer-events: none;
}

.tooltip-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: var(--primary-color);
}

.tooltip-content {
  font-size: 12px;
  margin-bottom: 5px;
  color: var(--text-color);
}

.tooltip-category {
  font-size: 11px;
  color: var(--text-lighter);
}

.tooltip-category span {
  color: var(--primary-color);
  font-weight: 500;
}

.node-detail-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 400px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  z-index: 50;
  max-height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8fafc;
}

.panel-header input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  font-size: 14px;
  color: #334155;
  background-color: white;
}

.panel-header input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.panel-actions {
  display: flex;
  gap: 10px;
  margin-left: 15px;
}

.action-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-button {
  background-color: #4CAF50;
  color: white;
}

.action-button:hover {
  background-color: #45a049;
}

.cancel-button {
  background-color: #f44336;
  color: white;
}

.cancel-button:hover {
  background-color: #d32f2f;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #64748b;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 8px;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.panel-content {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
}

.node-content, .node-relations {
  margin-bottom: 20px;
}

.node-content h4, .node-relations h4 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 16px;
  color: #334155;
  font-weight: 600;
  border-left: 3px solid #3b82f6;
  padding-left: 8px;
}

.content-editor {
  width: 100%;
  min-height: 150px;
  padding: 12px;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.6;
  color: #334155;
  background-color: white;
  resize: vertical;
  font-family: inherit;
}

.content-editor:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.node-relations ul {
  margin: 0;
  padding-left: 20px;
  list-style-type: disc;
}

.node-relations li {
  margin-bottom: 8px;
  cursor: pointer;
  color: #3b82f6;
}

.node-relations li:hover {
  color: #2563eb;
  text-decoration: underline;
}

.relation-type {
  font-size: 12px;
  color: #64748b;
  margin-left: 4px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.search-button {
  background-color: #f8fafc;
  color: #334155;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s;
}

.search-button:hover {
  background-color: #f1f5f9;
  border-color: #94a3b8;
}

.search-icon {
  margin-right: 6px;
  font-size: 16px;
}

.search-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 4px;
  margin-top: 12px;
  color: #64748b;
}

.search-spinner {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

/* 添加平滑过渡动画 */
@keyframes panel-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.node-detail-panel {
  animation: panel-fade-in 0.2s ease-out;
}
</style> 