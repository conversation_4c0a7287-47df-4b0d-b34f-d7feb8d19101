<template>
  <div class="student-courses">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">我的课程</h1>
        <p class="page-subtitle">管理您的学习课程，跟踪学习进度</p>
      </div>
      <div class="header-actions">
        <button class="btn-primary" @click="showEnrollDialog = true">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <path d="M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
          注册新课程
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters-section">
      <div class="search-box">
        <svg class="search-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
          <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索课程..."
          class="search-input"
        />
      </div>
      <div class="filter-controls">
        <select v-model="selectedCategory" class="filter-select">
          <option value="">所有分类</option>
          <option value="programming">编程开发</option>
          <option value="math">数学</option>
          <option value="science">科学</option>
          <option value="language">语言学习</option>
        </select>
        <select v-model="selectedStatus" class="filter-select">
          <option value="">所有状态</option>
          <option value="in-progress">进行中</option>
          <option value="completed">已完成</option>
          <option value="not-started">未开始</option>
        </select>
        <select v-model="sortBy" class="filter-select">
          <option value="recent">最近访问</option>
          <option value="progress">学习进度</option>
          <option value="name">课程名称</option>
          <option value="date">注册时间</option>
        </select>
      </div>
    </div>

    <!-- 课程统计 -->
    <div class="course-stats">
      <div class="stat-item">
        <div class="stat-number">{{ enrolledCourses.length }}</div>
        <div class="stat-label">已注册课程</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ completedCourses }}</div>
        <div class="stat-label">已完成课程</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ inProgressCourses }}</div>
        <div class="stat-label">进行中课程</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ averageProgress }}%</div>
        <div class="stat-label">平均进度</div>
      </div>
    </div>

    <!-- 课程列表 -->
    <div class="courses-section">
      <div class="section-header">
        <h2 class="section-title">课程列表</h2>
        <div class="view-toggle">
          <button
            :class="['view-btn', { active: viewMode === 'grid' }]"
            @click="viewMode = 'grid'"
          >
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
              <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
              <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
              <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
          <button
            :class="['view-btn', { active: viewMode === 'list' }]"
            @click="viewMode = 'list'"
          >
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 6H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M8 12H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M8 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M3 6H3.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M3 12H3.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M3 18H3.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="courses-grid">
        <div
          v-for="course in filteredCourses"
          :key="course.id"
          class="course-card"
          @click="openCourse(course.id)"
        >
          <div class="course-thumbnail">
            <img :src="course.thumbnail" :alt="course.title" />
            <div class="course-status" :class="course.status">
              {{ getStatusText(course.status) }}
            </div>
          </div>
          <div class="course-content">
            <div class="course-category">{{ course.category }}</div>
            <h3 class="course-title">{{ course.title }}</h3>
            <p class="course-instructor">{{ course.instructor }}</p>
            <div class="course-progress">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: course.progress + '%' }"></div>
              </div>
              <span class="progress-text">{{ course.progress }}%</span>
            </div>
            <div class="course-meta">
              <span class="course-lessons">{{ course.totalLessons }} 课时</span>
              <span class="course-duration">{{ course.duration }}</span>
            </div>
          </div>
          <div class="course-actions">
            <button
              v-if="course.status === 'in-progress'"
              class="btn-continue"
              @click.stop="continueCourse(course.id)"
            >
              继续学习
            </button>
            <button
              v-else-if="course.status === 'not-started'"
              class="btn-start"
              @click.stop="startCourse(course.id)"
            >
              开始学习
            </button>
            <button
              v-else
              class="btn-review"
              @click.stop="reviewCourse(course.id)"
            >
              复习课程
            </button>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="courses-list">
        <div
          v-for="course in filteredCourses"
          :key="course.id"
          class="course-row"
          @click="openCourse(course.id)"
        >
          <div class="course-info">
            <div class="course-thumbnail-small">
              <img :src="course.thumbnail" :alt="course.title" />
            </div>
            <div class="course-details">
              <div class="course-title-row">
                <h3 class="course-title">{{ course.title }}</h3>
                <div class="course-status" :class="course.status">
                  {{ getStatusText(course.status) }}
                </div>
              </div>
              <p class="course-instructor">{{ course.instructor }}</p>
              <div class="course-meta">
                <span class="course-category">{{ course.category }}</span>
                <span class="course-lessons">{{ course.totalLessons }} 课时</span>
                <span class="course-duration">{{ course.duration }}</span>
              </div>
            </div>
          </div>
          <div class="course-progress-section">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: course.progress + '%' }"></div>
            </div>
            <span class="progress-text">{{ course.progress }}%</span>
          </div>
          <div class="course-actions">
            <button
              v-if="course.status === 'in-progress'"
              class="btn-continue"
              @click.stop="continueCourse(course.id)"
            >
              继续学习
            </button>
            <button
              v-else-if="course.status === 'not-started'"
              class="btn-start"
              @click.stop="startCourse(course.id)"
            >
              开始学习
            </button>
            <button
              v-else
              class="btn-review"
              @click.stop="reviewCourse(course.id)"
            >
              复习课程
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredCourses.length === 0" class="empty-state">
        <svg class="empty-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M2 3H8C9.06087 3 10.0783 3.42143 10.8284 4.17157C11.5786 4.92172 12 5.93913 12 7V21C12 20.2044 11.6839 19.4413 11.1213 18.8787C10.5587 18.3161 9.79565 18 9 18H2V3Z" stroke="currentColor" stroke-width="2"/>
          <path d="M22 3H16C14.9391 3 13.9217 3.42143 13.1716 4.17157C12.4214 4.92172 12 5.93913 12 7V21C12 20.2044 12.3161 19.4413 12.8787 18.8787C13.4413 18.3161 14.2044 18 15 18H22V3Z" stroke="currentColor" stroke-width="2"/>
        </svg>
        <h3 class="empty-title">暂无课程</h3>
        <p class="empty-description">
          {{ searchQuery || selectedCategory || selectedStatus ? '没有找到符合条件的课程' : '您还没有注册任何课程' }}
        </p>
        <button v-if="!searchQuery && !selectedCategory && !selectedStatus" class="btn-primary" @click="showEnrollDialog = true">
          注册第一门课程
        </button>
      </div>
    </div>

    <!-- 注册课程对话框 -->
    <div v-if="showEnrollDialog" class="dialog-overlay" @click="showEnrollDialog = false">
      <div class="dialog" @click.stop>
        <div class="dialog-header">
          <h3 class="dialog-title">注册新课程</h3>
          <button class="dialog-close" @click="showEnrollDialog = false">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
        <div class="dialog-content">
          <p>这里将显示可注册的课程列表。</p>
          <p>功能开发中...</p>
        </div>
        <div class="dialog-actions">
          <button class="btn-secondary" @click="showEnrollDialog = false">取消</button>
          <button class="btn-primary" @click="enrollInCourse">确认注册</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'StudentCoursesPage',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const searchQuery = ref('')
    const selectedCategory = ref('')
    const selectedStatus = ref('')
    const sortBy = ref('recent')
    const viewMode = ref('grid')
    const showEnrollDialog = ref(false)
    
    // 课程数据
    const enrolledCourses = ref([
      {
        id: 1,
        title: '数据结构与算法',
        instructor: '张教授',
        category: '编程开发',
        progress: 65,
        status: 'in-progress',
        totalLessons: 24,
        duration: '12周',
        thumbnail: '/api/placeholder/300/200',
        lastAccessed: new Date(Date.now() - 2 * 60 * 60 * 1000)
      },
      {
        id: 2,
        title: 'Python编程基础',
        instructor: '李老师',
        category: '编程开发',
        progress: 100,
        status: 'completed',
        totalLessons: 18,
        duration: '8周',
        thumbnail: '/api/placeholder/300/200',
        lastAccessed: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
      },
      {
        id: 3,
        title: '计算机网络',
        instructor: '王教授',
        category: '编程开发',
        progress: 20,
        status: 'in-progress',
        totalLessons: 20,
        duration: '10周',
        thumbnail: '/api/placeholder/300/200',
        lastAccessed: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
      },
      {
        id: 4,
        title: '高等数学',
        instructor: '赵教授',
        category: '数学',
        progress: 0,
        status: 'not-started',
        totalLessons: 30,
        duration: '16周',
        thumbnail: '/api/placeholder/300/200',
        lastAccessed: null
      },
      {
        id: 5,
        title: '英语口语提升',
        instructor: 'Smith老师',
        category: '语言学习',
        progress: 45,
        status: 'in-progress',
        totalLessons: 15,
        duration: '6周',
        thumbnail: '/api/placeholder/300/200',
        lastAccessed: new Date(Date.now() - 5 * 60 * 60 * 1000)
      }
    ])

    // 计算属性
    const filteredCourses = computed(() => {
      let courses = enrolledCourses.value

      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        courses = courses.filter(course =>
          course.title.toLowerCase().includes(query) ||
          course.instructor.toLowerCase().includes(query) ||
          course.category.toLowerCase().includes(query)
        )
      }

      // 分类过滤
      if (selectedCategory.value) {
        courses = courses.filter(course => course.category === selectedCategory.value)
      }

      // 状态过滤
      if (selectedStatus.value) {
        courses = courses.filter(course => course.status === selectedStatus.value)
      }

      // 排序
      courses.sort((a, b) => {
        switch (sortBy.value) {
          case 'recent':
            if (!a.lastAccessed) return 1
            if (!b.lastAccessed) return -1
            return b.lastAccessed - a.lastAccessed
          case 'progress':
            return b.progress - a.progress
          case 'name':
            return a.title.localeCompare(b.title)
          case 'date':
            return b.id - a.id // 假设ID越大注册时间越晚
          default:
            return 0
        }
      })

      return courses
    })

    const completedCourses = computed(() => {
      return enrolledCourses.value.filter(course => course.status === 'completed').length
    })

    const inProgressCourses = computed(() => {
      return enrolledCourses.value.filter(course => course.status === 'in-progress').length
    })

    const averageProgress = computed(() => {
      if (enrolledCourses.value.length === 0) return 0
      const total = enrolledCourses.value.reduce((sum, course) => sum + course.progress, 0)
      return Math.round(total / enrolledCourses.value.length)
    })

    // 方法
    const getStatusText = (status) => {
      const statusMap = {
        'in-progress': '进行中',
        'completed': '已完成',
        'not-started': '未开始'
      }
      return statusMap[status] || status
    }

    const openCourse = (courseId) => {
      // 跳转到课程详情页
      router.push(`/student/courses/${courseId}`)
    }

    const continueCourse = (courseId) => {
      // 继续学习课程
      router.push(`/student/courses/${courseId}/learn`)
    }

    const startCourse = (courseId) => {
      // 开始学习课程
      router.push(`/student/courses/${courseId}/learn`)
    }

    const reviewCourse = (courseId) => {
      // 复习课程
      router.push(`/student/courses/${courseId}/review`)
    }

    const enrollInCourse = () => {
      // 注册课程逻辑
      showEnrollDialog.value = false
      // TODO: 实现注册逻辑
    }

    onMounted(() => {
      console.log('学生课程页面已加载')
    })

    return {
      searchQuery,
      selectedCategory,
      selectedStatus,
      sortBy,
      viewMode,
      showEnrollDialog,
      enrolledCourses,
      filteredCourses,
      completedCourses,
      inProgressCourses,
      averageProgress,
      getStatusText,
      openCourse,
      continueCourse,
      startCourse,
      reviewCourse,
      enrollInCourse
    }
  }
}
</script>

<style scoped>
.student-courses {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.page-subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.btn-primary {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--primary-500);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.btn-primary:hover {
  background-color: var(--primary-600);
}

.btn-primary svg {
  width: 16px;
  height: 16px;
}

/* 搜索和筛选 */
.filters-section {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--text-tertiary);
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-500);
}

.filter-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.filter-select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  transition: border-color var(--transition-fast);
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-500);
}

/* 课程统计 */
.course-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-item {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.stat-number {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--primary-500);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 课程列表区域 */
.courses-section {
  margin-bottom: var(--spacing-xl);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

.section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.view-toggle {
  display: flex;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.view-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: none;
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.view-btn:hover {
  background-color: var(--bg-secondary);
}

.view-btn.active {
  background-color: var(--primary-500);
  color: white;
}

.view-btn svg {
  width: 16px;
  height: 16px;
}

/* 网格视图 */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-lg);
}

.course-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.course-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.course-thumbnail {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.course-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-status {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: white;
}

.course-status.in-progress {
  background-color: var(--primary-500);
}

.course-status.completed {
  background-color: var(--success-500);
}

.course-status.not-started {
  background-color: var(--text-tertiary);
}

.course-content {
  padding: var(--spacing-lg);
}

.course-category {
  font-size: var(--text-xs);
  color: var(--primary-500);
  font-weight: var(--font-medium);
  text-transform: uppercase;
  margin-bottom: var(--spacing-xs);
}

.course-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.4;
}

.course-instructor {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-md) 0;
}

.course-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
}

.progress-bar {
  flex: 1;
  height: 6px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--primary-500);
  transition: width var(--transition-normal);
}

.progress-text {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  min-width: 35px;
}

.course-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-md);
}

.course-actions {
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.btn-continue,
.btn-start,
.btn-review {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.btn-continue {
  background-color: var(--primary-500);
  color: white;
}

.btn-continue:hover {
  background-color: var(--primary-600);
}

.btn-start {
  background-color: var(--success-500);
  color: white;
}

.btn-start:hover {
  background-color: var(--success-600);
}

.btn-review {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.btn-review:hover {
  background-color: var(--bg-secondary);
}

/* 列表视图 */
.courses-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.course-row {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.course-row:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.course-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.course-thumbnail-small {
  width: 80px;
  height: 60px;
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--bg-tertiary);
  flex-shrink: 0;
}

.course-thumbnail-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-details {
  flex: 1;
}

.course-title-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

.course-progress-section {
  width: 120px;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-lg);
  color: var(--text-tertiary);
}

.empty-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.empty-description {
  font-size: var(--text-base);
  margin: 0 0 var(--spacing-lg) 0;
}

/* 对话框 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.dialog {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.dialog-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.dialog-close {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--text-secondary);
  cursor: pointer;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.dialog-close:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.dialog-close svg {
  width: 16px;
  height: 16px;
}

.dialog-content {
  padding: var(--spacing-lg);
}

.dialog-actions {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  justify-content: flex-end;
}

.btn-secondary {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.btn-secondary:hover {
  background-color: var(--bg-tertiary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .student-courses {
    padding: var(--spacing-md);
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .filters-section {
    flex-direction: column;
  }

  .search-box {
    min-width: auto;
  }

  .filter-controls {
    flex-wrap: wrap;
  }

  .course-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
  }

  .courses-grid {
    grid-template-columns: 1fr;
  }

  .course-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .course-info {
    width: 100%;
  }

  .course-progress-section {
    width: 100%;
  }

  .course-actions {
    width: 100%;
  }

  .course-actions button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .course-stats {
    grid-template-columns: 1fr;
  }

  .stat-item {
    padding: var(--spacing-md);
  }

  .course-card {
    margin: 0 -var(--spacing-sm);
  }

  .course-content {
    padding: var(--spacing-md);
  }

  .course-actions {
    padding: 0 var(--spacing-md) var(--spacing-md);
  }
}
</style>
