<template>
  <div class="student-dashboard">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <h1 class="welcome-title">欢迎回来，{{ currentUser.name || currentUser.username || '同学' }}！</h1>
        <p class="welcome-subtitle">继续您的学习之旅，探索知识的海洋</p>
      </div>
      <div class="welcome-illustration">
        <svg viewBox="0 0 200 150" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="20" y="40" width="160" height="100" rx="8" fill="var(--primary-100)" stroke="var(--primary-300)" stroke-width="2"/>
          <circle cx="60" cy="80" r="15" fill="var(--primary-500)"/>
          <rect x="85" y="70" width="80" height="8" rx="4" fill="var(--primary-300)"/>
          <rect x="85" y="85" width="60" height="6" rx="3" fill="var(--primary-200)"/>
          <rect x="85" y="98" width="70" height="6" rx="3" fill="var(--primary-200)"/>
        </svg>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon courses">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 3H8C9.06087 3 10.0783 3.42143 10.8284 4.17157C11.5786 4.92172 12 5.93913 12 7V21C12 20.2044 11.6839 19.4413 11.1213 18.8787C10.5587 18.3161 9.79565 18 9 18H2V3Z" stroke="currentColor" stroke-width="2"/>
            <path d="M22 3H16C14.9391 3 13.9217 3.42143 13.1716 4.17157C12.4214 4.92172 12 5.93913 12 7V21C12 20.2044 12.3161 19.4413 12.8787 18.8787C13.4413 18.3161 14.2044 18 15 18H22V3Z" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.enrolledCourses }}</div>
          <div class="stat-label">已注册课程</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon progress">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M22 12H18L15 21L9 3L6 12H2" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.completedLessons }}</div>
          <div class="stat-label">已完成课时</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon time">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.studyHours }}h</div>
          <div class="stat-label">学习时长</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon achievement">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.achievements }}</div>
          <div class="stat-label">获得成就</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content-grid">
      <!-- 最近活动 -->
      <div class="content-card recent-activity">
        <div class="card-header">
          <h2 class="card-title">最近活动</h2>
          <router-link to="/student/progress" class="view-all-link">查看全部</router-link>
        </div>
        <div class="activity-list">
          <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
            <div class="activity-icon" :class="activity.type">
              <svg v-if="activity.type === 'lesson'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2"/>
              </svg>
              <svg v-else-if="activity.type === 'quiz'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2"/>
                <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
              </svg>
              <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-description">{{ activity.description }}</div>
              <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 进行中的课程 -->
      <div class="content-card ongoing-courses">
        <div class="card-header">
          <h2 class="card-title">进行中的课程</h2>
          <router-link to="/student/courses" class="view-all-link">查看全部</router-link>
        </div>
        <div class="course-list">
          <div v-for="course in ongoingCourses" :key="course.id" class="course-item">
            <div class="course-thumbnail">
              <img :src="course.thumbnail" :alt="course.title" />
            </div>
            <div class="course-content">
              <div class="course-title">{{ course.title }}</div>
              <div class="course-instructor">{{ course.instructor }}</div>
              <div class="course-progress">
                <div class="progress-bar">
                  <div class="progress-fill" :style="{ width: course.progress + '%' }"></div>
                </div>
                <span class="progress-text">{{ course.progress }}%</span>
              </div>
            </div>
            <button class="continue-btn" @click="continueCourse(course.id)">继续学习</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions">
      <h2 class="section-title">快速操作</h2>
      <div class="action-grid">
        <router-link to="/student/courses" class="action-card">
          <div class="action-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2 3H8C9.06087 3 10.0783 3.42143 10.8284 4.17157C11.5786 4.92172 12 5.93913 12 7V21C12 20.2044 11.6839 19.4413 11.1213 18.8787C10.5587 18.3161 9.79565 18 9 18H2V3Z" stroke="currentColor" stroke-width="2"/>
              <path d="M22 3H16C14.9391 3 13.9217 3.42143 13.1716 4.17157C12.4214 4.92172 12 5.93913 12 7V21C12 20.2044 12.3161 19.4413 12.8787 18.8787C13.4413 18.3161 14.2044 18 15 18H22V3Z" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="action-content">
            <div class="action-title">浏览课程</div>
            <div class="action-description">发现新的学习内容</div>
          </div>
        </router-link>

        <router-link to="/student/knowledge-graph" class="action-card">
          <div class="action-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
              <circle cx="12" cy="3" r="1" stroke="currentColor" stroke-width="2"/>
              <circle cx="12" cy="21" r="1" stroke="currentColor" stroke-width="2"/>
              <circle cx="3" cy="12" r="1" stroke="currentColor" stroke-width="2"/>
              <circle cx="21" cy="12" r="1" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="action-content">
            <div class="action-title">知识图谱</div>
            <div class="action-description">探索知识结构</div>
          </div>
        </router-link>

        <router-link to="/student/progress" class="action-card">
          <div class="action-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22 12H18L15 21L9 3L6 12H2" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="action-content">
            <div class="action-title">学习进度</div>
            <div class="action-description">查看学习统计</div>
          </div>
        </router-link>

        <router-link to="/student/materials" class="action-card">
          <div class="action-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
              <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="action-content">
            <div class="action-title">学习资料</div>
            <div class="action-description">访问学习材料</div>
          </div>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getCurrentUser } from '@/api/auth'

export default {
  name: 'StudentDashboardPage',
  setup() {
    const router = useRouter()
    const currentUser = ref(getCurrentUser() || {})
    
    // 统计数据
    const stats = ref({
      enrolledCourses: 5,
      completedLessons: 23,
      studyHours: 45,
      achievements: 8
    })
    
    // 最近活动
    const recentActivities = ref([
      {
        id: 1,
        type: 'lesson',
        title: '完成了《数据结构》第3章',
        description: '学习了栈和队列的基本概念',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小时前
      },
      {
        id: 2,
        type: 'quiz',
        title: '通过了《算法基础》测验',
        description: '得分：85/100',
        timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) // 1天前
      },
      {
        id: 3,
        type: 'material',
        title: '下载了学习资料',
        description: '《Python编程指南》PDF文档',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2天前
      }
    ])
    
    // 进行中的课程
    const ongoingCourses = ref([
      {
        id: 1,
        title: '数据结构与算法',
        instructor: '张教授',
        progress: 65,
        thumbnail: '/api/placeholder/80/60'
      },
      {
        id: 2,
        title: 'Python编程基础',
        instructor: '李老师',
        progress: 40,
        thumbnail: '/api/placeholder/80/60'
      },
      {
        id: 3,
        title: '计算机网络',
        instructor: '王教授',
        progress: 20,
        thumbnail: '/api/placeholder/80/60'
      }
    ])

    // 格式化时间
    const formatTime = (timestamp) => {
      const now = new Date()
      const diff = now - timestamp
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(hours / 24)
      
      if (days > 0) {
        return `${days}天前`
      } else if (hours > 0) {
        return `${hours}小时前`
      } else {
        return '刚刚'
      }
    }

    // 继续学习课程
    const continueCourse = (courseId) => {
      // 这里可以跳转到具体的课程学习页面
      router.push(`/student/courses/${courseId}`)
    }

    onMounted(() => {
      // 这里可以加载真实的数据
      console.log('学生仪表板已加载')
    })

    return {
      currentUser,
      stats,
      recentActivities,
      ongoingCourses,
      formatTime,
      continueCourse
    }
  }
}
</script>

<style scoped>
.student-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

/* 欢迎区域 */
.welcome-section {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
  position: relative;
  overflow: hidden;
}

.welcome-content {
  flex: 1;
}

.welcome-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  margin: 0 0 var(--spacing-sm) 0;
}

.welcome-subtitle {
  font-size: var(--text-lg);
  opacity: 0.9;
  margin: 0;
}

.welcome-illustration {
  width: 200px;
  height: 150px;
  opacity: 0.8;
}

.welcome-illustration svg {
  width: 100%;
  height: 100%;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all var(--transition-fast);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.courses {
  background-color: var(--primary-500);
}

.stat-icon.progress {
  background-color: var(--success-500);
}

.stat-icon.time {
  background-color: var(--warning-500);
}

.stat-icon.achievement {
  background-color: var(--accent-500);
}

.stat-icon svg {
  width: 24px;
  height: 24px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

/* 主要内容网格 */
.main-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.content-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.view-all-link {
  color: var(--primary-500);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  transition: color var(--transition-fast);
}

.view-all-link:hover {
  color: var(--primary-600);
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.activity-item:hover {
  background-color: var(--bg-secondary);
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.activity-icon.lesson {
  background-color: var(--primary-500);
}

.activity-icon.quiz {
  background-color: var(--success-500);
}

.activity-icon.material {
  background-color: var(--accent-500);
}

.activity-icon svg {
  width: 16px;
  height: 16px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.activity-description {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.activity-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

/* 课程列表 */
.course-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.course-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.course-item:hover {
  background-color: var(--bg-secondary);
}

.course-thumbnail {
  width: 60px;
  height: 45px;
  border-radius: var(--radius-sm);
  overflow: hidden;
  background-color: var(--bg-tertiary);
  flex-shrink: 0;
}

.course-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.course-content {
  flex: 1;
}

.course-title {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.course-instructor {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.course-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.progress-bar {
  flex: 1;
  height: 4px;
  background-color: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--primary-500);
  transition: width var(--transition-normal);
}

.progress-text {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  min-width: 30px;
}

.continue-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--primary-500);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.continue-btn:hover {
  background-color: var(--primary-600);
}

/* 快速操作 */
.quick-actions {
  margin-bottom: var(--spacing-xl);
}

.section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.action-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all var(--transition-fast);
}

.action-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  text-decoration: none;
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  background-color: var(--primary-100);
  color: var(--primary-600);
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon svg {
  width: 24px;
  height: 24px;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.action-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .student-dashboard {
    padding: var(--spacing-md);
  }

  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-lg);
  }

  .welcome-illustration {
    width: 150px;
    height: 112px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }

  .main-content-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .action-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: var(--spacing-md);
  }

  .content-card {
    padding: var(--spacing-md);
  }

  .welcome-section {
    padding: var(--spacing-lg);
  }

  .welcome-title {
    font-size: var(--text-xl);
  }

  .welcome-subtitle {
    font-size: var(--text-base);
  }
}
</style>
