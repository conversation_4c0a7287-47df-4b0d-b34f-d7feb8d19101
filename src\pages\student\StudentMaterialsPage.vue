<template>
  <div class="student-materials">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">学习资料</h1>
        <p class="page-subtitle">访问课程相关的学习材料和资源</p>
      </div>
      <div class="header-actions">
        <button class="btn-secondary" @click="refreshMaterials">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1 4V10H7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M23 20V14H17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M20.49 9C19.9828 7.56678 19.1209 6.28392 17.9845 5.27304C16.8482 4.26216 15.4745 3.55682 13.9917 3.21834C12.5089 2.87986 10.9652 2.91902 9.50481 3.33329C8.04437 3.74757 6.71475 4.52306 5.64 5.58L1 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M3.51 15C4.01719 16.4332 4.87906 17.7161 6.01545 18.727C7.15184 19.7378 8.52547 20.4432 10.0083 20.7817C11.4911 21.1201 13.0348 21.081 14.4952 20.6667C15.9556 20.2524 17.2852 19.4769 18.36 18.42L23 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          刷新
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters-section">
      <div class="search-box">
        <svg class="search-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
          <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索学习资料..."
          class="search-input"
        />
      </div>
      <div class="filter-controls">
        <select v-model="selectedType" class="filter-select">
          <option value="">所有类型</option>
          <option value="pdf">PDF文档</option>
          <option value="video">视频资料</option>
          <option value="audio">音频资料</option>
          <option value="image">图片资料</option>
          <option value="link">外部链接</option>
        </select>
        <select v-model="selectedCourse" class="filter-select">
          <option value="">所有课程</option>
          <option v-for="course in courses" :key="course.id" :value="course.id">
            {{ course.name }}
          </option>
        </select>
        <select v-model="sortBy" class="filter-select">
          <option value="recent">最近添加</option>
          <option value="name">资料名称</option>
          <option value="type">资料类型</option>
          <option value="size">文件大小</option>
        </select>
      </div>
    </div>

    <!-- 资料统计 -->
    <div class="materials-stats">
      <div class="stat-item">
        <div class="stat-number">{{ totalMaterials }}</div>
        <div class="stat-label">总资料数</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ downloadedMaterials }}</div>
        <div class="stat-label">已下载</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ totalSize }}</div>
        <div class="stat-label">总大小</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ recentMaterials }}</div>
        <div class="stat-label">本周新增</div>
      </div>
    </div>

    <!-- 资料列表 -->
    <div class="materials-section">
      <div class="section-header">
        <h2 class="section-title">学习资料列表</h2>
        <div class="view-toggle">
          <button
            :class="['view-btn', { active: viewMode === 'grid' }]"
            @click="viewMode = 'grid'"
          >
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
              <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2"/>
              <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
              <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2"/>
            </svg>
          </button>
          <button
            :class="['view-btn', { active: viewMode === 'list' }]"
            @click="viewMode = 'list'"
          >
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 6H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M8 12H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M8 18H21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M3 6H3.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M3 12H3.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M3 18H3.01" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="materials-grid">
        <div
          v-for="material in filteredMaterials"
          :key="material.id"
          class="material-card"
          @click="openMaterial(material)"
        >
          <div class="material-icon" :class="material.type">
            <svg v-if="material.type === 'pdf'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
              <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
              <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2"/>
              <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2"/>
              <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2"/>
            </svg>
            <svg v-else-if="material.type === 'video'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <polygon points="23 7 16 12 23 17 23 7" stroke="currentColor" stroke-width="2"/>
              <rect x="1" y="5" width="15" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
            </svg>
            <svg v-else-if="material.type === 'audio'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 18V5L21 12L9 18Z" stroke="currentColor" stroke-width="2"/>
              <circle cx="9" cy="12" r="2" stroke="currentColor" stroke-width="2"/>
            </svg>
            <svg v-else-if="material.type === 'image'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
              <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
              <polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2"/>
            </svg>
            <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 13C10.4295 13.5741 10.9774 14.0491 11.6066 14.3929C12.2357 14.7367 12.9315 14.9411 13.6467 14.9923C14.3618 15.0435 15.0796 14.9403 15.7513 14.6897C16.4231 14.4392 17.0331 14.047 17.54 13.54L20.54 10.54C21.4508 9.59695 21.9548 8.33394 21.9434 7.02296C21.932 5.71198 21.4061 4.45791 20.4791 3.53087C19.5521 2.60383 18.298 2.07799 16.987 2.0666C15.676 2.0552 14.413 2.55918 13.47 3.47L11.75 5.18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M14 11C13.5705 10.4259 13.0226 9.95085 12.3934 9.60706C11.7643 9.26327 11.0685 9.05892 10.3533 9.00771C9.63819 8.9565 8.92037 9.05973 8.24864 9.31028C7.5769 9.56083 6.9669 9.95303 6.46 10.46L3.46 13.46C2.54918 14.403 2.04519 15.6661 2.05659 16.977C2.06798 18.288 2.59382 19.5421 3.52087 20.4691C4.44791 21.3962 5.70198 21.922 7.01296 21.9334C8.32394 21.9448 9.58695 21.4408 10.53 20.53L12.24 18.82" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <div class="material-content">
            <div class="material-title">{{ material.title }}</div>
            <div class="material-course">{{ material.courseName }}</div>
            <div class="material-meta">
              <span class="material-size">{{ formatFileSize(material.size) }}</span>
              <span class="material-date">{{ formatDate(material.uploadDate) }}</span>
            </div>
          </div>
          <div class="material-actions">
            <button class="action-btn" @click.stop="downloadMaterial(material)" :title="'下载 ' + material.title">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="materials-list">
        <div class="list-header">
          <div class="header-cell name">名称</div>
          <div class="header-cell course">课程</div>
          <div class="header-cell type">类型</div>
          <div class="header-cell size">大小</div>
          <div class="header-cell date">上传时间</div>
          <div class="header-cell actions">操作</div>
        </div>
        <div
          v-for="material in filteredMaterials"
          :key="material.id"
          class="list-item"
          @click="openMaterial(material)"
        >
          <div class="list-cell name">
            <div class="material-icon-small" :class="material.type">
              <svg v-if="material.type === 'pdf'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
              </svg>
              <svg v-else-if="material.type === 'video'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <polygon points="23 7 16 12 23 17 23 7" stroke="currentColor" stroke-width="2"/>
                <rect x="1" y="5" width="15" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
              </svg>
              <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <span class="material-title">{{ material.title }}</span>
          </div>
          <div class="list-cell course">{{ material.courseName }}</div>
          <div class="list-cell type">
            <span class="type-badge" :class="material.type">{{ getTypeLabel(material.type) }}</span>
          </div>
          <div class="list-cell size">{{ formatFileSize(material.size) }}</div>
          <div class="list-cell date">{{ formatDate(material.uploadDate) }}</div>
          <div class="list-cell actions">
            <button class="action-btn" @click.stop="downloadMaterial(material)" :title="'下载 ' + material.title">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <polyline points="7,10 12,15 17,10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredMaterials.length === 0" class="empty-state">
        <div class="empty-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2"/>
            <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="empty-title">暂无学习资料</div>
        <div class="empty-description">
          {{ searchQuery ? '没有找到匹配的学习资料' : '您还没有任何学习资料，请联系老师获取相关资料' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { getCurrentUser } from '@/api/auth'

export default {
  name: 'StudentMaterialsPage',
  setup() {
    const currentUser = ref(getCurrentUser() || {})
    
    // 响应式数据
    const searchQuery = ref('')
    const selectedType = ref('')
    const selectedCourse = ref('')
    const sortBy = ref('recent')
    const viewMode = ref('grid')
    
    // 模拟数据
    const materials = ref([
      {
        id: 1,
        title: 'Python基础教程.pdf',
        type: 'pdf',
        courseName: 'Python编程基础',
        courseId: 1,
        size: 2048576, // 2MB
        uploadDate: new Date('2024-01-15'),
        downloadUrl: '/materials/python-basics.pdf'
      },
      {
        id: 2,
        title: '数据结构讲解视频',
        type: 'video',
        courseName: '数据结构与算法',
        courseId: 2,
        size: 104857600, // 100MB
        uploadDate: new Date('2024-01-10'),
        downloadUrl: '/materials/data-structures-video.mp4'
      },
      {
        id: 3,
        title: '算法复杂度分析',
        type: 'pdf',
        courseName: '数据结构与算法',
        courseId: 2,
        size: 1048576, // 1MB
        uploadDate: new Date('2024-01-08'),
        downloadUrl: '/materials/algorithm-complexity.pdf'
      },
      {
        id: 4,
        title: '网络协议图解',
        type: 'image',
        courseName: '计算机网络',
        courseId: 3,
        size: 512000, // 500KB
        uploadDate: new Date('2024-01-05'),
        downloadUrl: '/materials/network-protocols.png'
      },
      {
        id: 5,
        title: 'MDN Web文档',
        type: 'link',
        courseName: 'Web开发基础',
        courseId: 4,
        size: 0,
        uploadDate: new Date('2024-01-01'),
        downloadUrl: 'https://developer.mozilla.org'
      }
    ])
    
    const courses = ref([
      { id: 1, name: 'Python编程基础' },
      { id: 2, name: '数据结构与算法' },
      { id: 3, name: '计算机网络' },
      { id: 4, name: 'Web开发基础' }
    ])

    // 计算属性
    const filteredMaterials = computed(() => {
      let filtered = materials.value

      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(material =>
          material.title.toLowerCase().includes(query) ||
          material.courseName.toLowerCase().includes(query)
        )
      }

      // 类型过滤
      if (selectedType.value) {
        filtered = filtered.filter(material => material.type === selectedType.value)
      }

      // 课程过滤
      if (selectedCourse.value) {
        filtered = filtered.filter(material => material.courseId === parseInt(selectedCourse.value))
      }

      // 排序
      filtered.sort((a, b) => {
        switch (sortBy.value) {
          case 'name':
            return a.title.localeCompare(b.title)
          case 'type':
            return a.type.localeCompare(b.type)
          case 'size':
            return b.size - a.size
          case 'recent':
          default:
            return new Date(b.uploadDate) - new Date(a.uploadDate)
        }
      })

      return filtered
    })

    const totalMaterials = computed(() => materials.value.length)
    const downloadedMaterials = computed(() => materials.value.filter(m => m.downloaded).length)
    const totalSize = computed(() => {
      const bytes = materials.value.reduce((sum, material) => sum + material.size, 0)
      return formatFileSize(bytes)
    })
    const recentMaterials = computed(() => {
      const oneWeekAgo = new Date()
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
      return materials.value.filter(material => new Date(material.uploadDate) > oneWeekAgo).length
    })

    // 方法
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const formatDate = (date) => {
      return new Date(date).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      })
    }

    const getTypeLabel = (type) => {
      const labels = {
        pdf: 'PDF',
        video: '视频',
        audio: '音频',
        image: '图片',
        link: '链接'
      }
      return labels[type] || type.toUpperCase()
    }

    const openMaterial = (material) => {
      if (material.type === 'link') {
        window.open(material.downloadUrl, '_blank')
      } else {
        downloadMaterial(material)
      }
    }

    const downloadMaterial = (material) => {
      // 模拟下载
      console.log('下载资料:', material.title)

      // 创建下载链接
      const link = document.createElement('a')
      link.href = material.downloadUrl
      link.download = material.title
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 标记为已下载
      material.downloaded = true
    }

    const refreshMaterials = () => {
      console.log('刷新学习资料')
      // 这里可以重新加载数据
    }

    onMounted(() => {
      console.log('学生学习资料页面已加载')
    })

    return {
      currentUser,
      searchQuery,
      selectedType,
      selectedCourse,
      sortBy,
      viewMode,
      materials,
      courses,
      filteredMaterials,
      totalMaterials,
      downloadedMaterials,
      totalSize,
      recentMaterials,
      formatFileSize,
      formatDate,
      getTypeLabel,
      openMaterial,
      downloadMaterial,
      refreshMaterials
    }
  }
}
</script>

<style scoped>
.student-materials {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.page-subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.btn-primary, .btn-secondary {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.btn-primary {
  background-color: var(--primary-500);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-600);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--bg-tertiary);
}

.btn-primary svg, .btn-secondary svg {
  width: 16px;
  height: 16px;
}

/* 搜索和筛选 */
.filters-section {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--text-tertiary);
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-500);
}

.filter-controls {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.filter-select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  transition: border-color var(--transition-fast);
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-500);
}

/* 资料统计 */
.materials-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.stat-item {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  text-align: center;
}

.stat-number {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--primary-500);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 资料列表区域 */
.materials-section {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

.section-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.view-toggle {
  display: flex;
  gap: var(--spacing-xs);
}

.view-btn {
  padding: var(--spacing-xs);
  border: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.view-btn:hover {
  background-color: var(--bg-tertiary);
}

.view-btn.active {
  background-color: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
}

.view-btn svg {
  width: 16px;
  height: 16px;
}

/* 网格视图 */
.materials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.material-card {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.material-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.material-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-sm);
  color: white;
}

.material-icon.pdf {
  background-color: var(--error-500);
}

.material-icon.video {
  background-color: var(--primary-500);
}

.material-icon.audio {
  background-color: var(--success-500);
}

.material-icon.image {
  background-color: var(--warning-500);
}

.material-icon.link {
  background-color: var(--accent-500);
}

.material-icon svg {
  width: 24px;
  height: 24px;
}

.material-content {
  flex: 1;
}

.material-title {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.4;
}

.material-course {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.material-meta {
  display: flex;
  justify-content: space-between;
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.material-actions {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.action-btn:hover {
  background-color: var(--primary-500);
  color: white;
}

.action-btn svg {
  width: 16px;
  height: 16px;
}

/* 列表视图 */
.materials-list {
  display: flex;
  flex-direction: column;
}

.list-header {
  display: grid;
  grid-template-columns: 2fr 1fr 80px 80px 100px 60px;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.list-item {
  display: grid;
  grid-template-columns: 2fr 1fr 80px 80px 100px 60px;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  align-items: center;
}

.list-item:hover {
  background-color: var(--bg-secondary);
}

.list-cell.name {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.material-icon-small {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.material-icon-small.pdf {
  background-color: var(--error-500);
}

.material-icon-small.video {
  background-color: var(--primary-500);
}

.material-icon-small.audio {
  background-color: var(--success-500);
}

.material-icon-small.image {
  background-color: var(--warning-500);
}

.material-icon-small.link {
  background-color: var(--accent-500);
}

.material-icon-small svg {
  width: 12px;
  height: 12px;
}

.type-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: white;
}

.type-badge.pdf {
  background-color: var(--error-500);
}

.type-badge.video {
  background-color: var(--primary-500);
}

.type-badge.audio {
  background-color: var(--success-500);
}

.type-badge.image {
  background-color: var(--warning-500);
}

.type-badge.link {
  background-color: var(--accent-500);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.empty-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-lg);
  color: var(--text-tertiary);
}

.empty-icon svg {
  width: 100%;
  height: 100%;
}

.empty-title {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  margin-bottom: var(--spacing-sm);
}

.empty-description {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .student-materials {
    padding: var(--spacing-md);
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .filters-section {
    flex-direction: column;
  }

  .filter-controls {
    justify-content: stretch;
  }

  .filter-select {
    flex: 1;
  }

  .materials-grid {
    grid-template-columns: 1fr;
  }

  .list-header,
  .list-item {
    grid-template-columns: 1fr;
    gap: var(--spacing-xs);
  }

  .list-header {
    display: none;
  }

  .list-item {
    flex-direction: column;
    align-items: flex-start;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
  }

  .list-cell {
    width: 100%;
  }

  .list-cell.name {
    margin-bottom: var(--spacing-xs);
  }

  .materials-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .materials-stats {
    grid-template-columns: 1fr;
  }

  .stat-item {
    padding: var(--spacing-md);
  }
}
</style>
