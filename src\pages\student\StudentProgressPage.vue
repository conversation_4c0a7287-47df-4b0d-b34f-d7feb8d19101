<template>
  <div class="student-progress">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">学习进度</h1>
        <p class="page-subtitle">跟踪您的学习成果，分析学习数据</p>
      </div>
      <div class="header-actions">
        <select v-model="timeRange" class="time-select">
          <option value="week">本周</option>
          <option value="month">本月</option>
          <option value="quarter">本季度</option>
          <option value="year">本年</option>
        </select>
      </div>
    </div>

    <!-- 总体统计 -->
    <div class="overview-stats">
      <div class="stat-card primary">
        <div class="stat-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M22 12H18L15 21L9 3L6 12H2" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ overallProgress }}%</div>
          <div class="stat-label">总体进度</div>
          <div class="stat-change positive">+5% 本周</div>
        </div>
      </div>

      <div class="stat-card success">
        <div class="stat-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2"/>
            <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ completedLessons }}</div>
          <div class="stat-label">已完成课时</div>
          <div class="stat-change positive">+3 本周</div>
        </div>
      </div>

      <div class="stat-card warning">
        <div class="stat-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ studyHours }}h</div>
          <div class="stat-label">学习时长</div>
          <div class="stat-change positive">+2.5h 本周</div>
        </div>
      </div>

      <div class="stat-card accent">
        <div class="stat-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ achievements }}</div>
          <div class="stat-label">获得成就</div>
          <div class="stat-change positive">+1 本周</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-container">
        <div class="chart-header">
          <h2 class="chart-title">学习时长趋势</h2>
          <div class="chart-legend">
            <div class="legend-item">
              <div class="legend-color" style="background-color: var(--primary-500);"></div>
              <span>每日学习时长</span>
            </div>
          </div>
        </div>
        <div class="chart-content">
          <!-- 模拟图表 -->
          <div class="mock-chart">
            <div class="chart-bars">
              <div class="bar" style="height: 60%;" data-value="2.5h"></div>
              <div class="bar" style="height: 80%;" data-value="3.2h"></div>
              <div class="bar" style="height: 45%;" data-value="1.8h"></div>
              <div class="bar" style="height: 90%;" data-value="3.6h"></div>
              <div class="bar" style="height: 70%;" data-value="2.8h"></div>
              <div class="bar" style="height: 55%;" data-value="2.2h"></div>
              <div class="bar" style="height: 85%;" data-value="3.4h"></div>
            </div>
            <div class="chart-labels">
              <span>周一</span>
              <span>周二</span>
              <span>周三</span>
              <span>周四</span>
              <span>周五</span>
              <span>周六</span>
              <span>周日</span>
            </div>
          </div>
        </div>
      </div>

      <div class="chart-container">
        <div class="chart-header">
          <h2 class="chart-title">课程进度分布</h2>
        </div>
        <div class="chart-content">
          <!-- 模拟饼图 -->
          <div class="mock-pie-chart">
            <div class="pie-center">
              <div class="pie-value">{{ overallProgress }}%</div>
              <div class="pie-label">总体进度</div>
            </div>
            <svg viewBox="0 0 200 200" class="pie-svg">
              <circle cx="100" cy="100" r="80" fill="none" stroke="var(--bg-tertiary)" stroke-width="20"/>
              <circle cx="100" cy="100" r="80" fill="none" stroke="var(--primary-500)" stroke-width="20"
                      stroke-dasharray="377" stroke-dashoffset="113" transform="rotate(-90 100 100)"/>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 课程进度详情 -->
    <div class="course-progress-section">
      <div class="section-header">
        <h2 class="section-title">课程进度详情</h2>
        <div class="sort-controls">
          <select v-model="sortBy" class="sort-select">
            <option value="progress">按进度排序</option>
            <option value="name">按名称排序</option>
            <option value="recent">按最近学习</option>
          </select>
        </div>
      </div>

      <div class="course-progress-list">
        <div v-for="course in sortedCourses" :key="course.id" class="course-progress-item">
          <div class="course-info">
            <div class="course-thumbnail">
              <img :src="course.thumbnail" :alt="course.title" />
            </div>
            <div class="course-details">
              <h3 class="course-title">{{ course.title }}</h3>
              <p class="course-instructor">{{ course.instructor }}</p>
              <div class="course-meta">
                <span class="course-lessons">{{ course.completedLessons }}/{{ course.totalLessons }} 课时</span>
                <span class="course-time">{{ course.studyTime }}h 学习时长</span>
              </div>
            </div>
          </div>
          <div class="progress-details">
            <div class="progress-bar-container">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: course.progress + '%' }"></div>
              </div>
              <span class="progress-percentage">{{ course.progress }}%</span>
            </div>
            <div class="progress-stats">
              <div class="stat-item">
                <span class="stat-label">本周进度</span>
                <span class="stat-value">+{{ course.weeklyProgress }}%</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">预计完成</span>
                <span class="stat-value">{{ course.estimatedCompletion }}</span>
              </div>
            </div>
          </div>
          <div class="course-actions">
            <button class="btn-continue" @click="continueCourse(course.id)">
              继续学习
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 成就展示 -->
    <div class="achievements-section">
      <div class="section-header">
        <h2 class="section-title">学习成就</h2>
        <router-link to="/student/achievements" class="view-all-link">查看全部</router-link>
      </div>
      <div class="achievements-grid">
        <div v-for="achievement in recentAchievements" :key="achievement.id" class="achievement-card" :class="{ unlocked: achievement.unlocked }">
          <div class="achievement-icon">
            <svg v-if="achievement.type === 'streak'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" stroke-width="2"/>
            </svg>
            <svg v-else-if="achievement.type === 'completion'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2"/>
              <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
            </svg>
            <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="achievement-content">
            <h3 class="achievement-title">{{ achievement.title }}</h3>
            <p class="achievement-description">{{ achievement.description }}</p>
            <div v-if="achievement.unlocked" class="achievement-date">
              {{ formatDate(achievement.unlockedAt) }}
            </div>
            <div v-else class="achievement-progress">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: achievement.progress + '%' }"></div>
              </div>
              <span class="progress-text">{{ achievement.progress }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 学习建议 -->
    <div class="suggestions-section">
      <h2 class="section-title">学习建议</h2>
      <div class="suggestions-list">
        <div v-for="suggestion in suggestions" :key="suggestion.id" class="suggestion-item">
          <div class="suggestion-icon" :class="suggestion.type">
            <svg v-if="suggestion.type === 'time'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
              <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2"/>
            </svg>
            <svg v-else-if="suggestion.type === 'focus'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
            </svg>
            <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22 12H18L15 21L9 3L6 12H2" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <div class="suggestion-content">
            <h3 class="suggestion-title">{{ suggestion.title }}</h3>
            <p class="suggestion-description">{{ suggestion.description }}</p>
          </div>
          <button v-if="suggestion.action" class="suggestion-action" @click="handleSuggestion(suggestion)">
            {{ suggestion.action }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'StudentProgressPage',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const timeRange = ref('week')
    const sortBy = ref('progress')
    
    // 统计数据
    const overallProgress = ref(70)
    const completedLessons = ref(23)
    const studyHours = ref(45)
    const achievements = ref(8)
    
    // 课程数据
    const courses = ref([
      {
        id: 1,
        title: '数据结构与算法',
        instructor: '张教授',
        progress: 85,
        completedLessons: 20,
        totalLessons: 24,
        studyTime: 18,
        weeklyProgress: 15,
        estimatedCompletion: '2周',
        thumbnail: '/api/placeholder/60/45',
        lastStudied: new Date(Date.now() - 2 * 60 * 60 * 1000)
      },
      {
        id: 2,
        title: 'Python编程基础',
        instructor: '李老师',
        progress: 100,
        completedLessons: 18,
        totalLessons: 18,
        studyTime: 15,
        weeklyProgress: 0,
        estimatedCompletion: '已完成',
        thumbnail: '/api/placeholder/60/45',
        lastStudied: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
      },
      {
        id: 3,
        title: '计算机网络',
        instructor: '王教授',
        progress: 35,
        completedLessons: 7,
        totalLessons: 20,
        studyTime: 12,
        weeklyProgress: 10,
        estimatedCompletion: '6周',
        thumbnail: '/api/placeholder/60/45',
        lastStudied: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
      }
    ])
    
    // 成就数据
    const recentAchievements = ref([
      {
        id: 1,
        title: '连续学习7天',
        description: '保持每日学习的好习惯',
        type: 'streak',
        unlocked: true,
        unlockedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
      },
      {
        id: 2,
        title: '完成第一门课程',
        description: '成功完成Python编程基础课程',
        type: 'completion',
        unlocked: true,
        unlockedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
      },
      {
        id: 3,
        title: '学习达人',
        description: '累计学习时长达到50小时',
        type: 'time',
        unlocked: false,
        progress: 90
      }
    ])
    
    // 学习建议
    const suggestions = ref([
      {
        id: 1,
        type: 'time',
        title: '增加学习时间',
        description: '建议每天增加30分钟学习时间，以提高学习效率',
        action: '设置提醒'
      },
      {
        id: 2,
        type: 'focus',
        title: '专注薄弱环节',
        description: '计算机网络课程进度较慢，建议重点关注',
        action: '查看课程'
      }
    ])

    // 计算属性
    const sortedCourses = computed(() => {
      const coursesCopy = [...courses.value]
      
      switch (sortBy.value) {
        case 'progress':
          return coursesCopy.sort((a, b) => b.progress - a.progress)
        case 'name':
          return coursesCopy.sort((a, b) => a.title.localeCompare(b.title))
        case 'recent':
          return coursesCopy.sort((a, b) => b.lastStudied - a.lastStudied)
        default:
          return coursesCopy
      }
    })

    // 方法
    const formatDate = (date) => {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric'
      })
    }

    const continueCourse = (courseId) => {
      router.push(`/student/courses/${courseId}/learn`)
    }

    const handleSuggestion = (suggestion) => {
      if (suggestion.action === '设置提醒') {
        // 设置学习提醒
        console.log('设置学习提醒')
      } else if (suggestion.action === '查看课程') {
        // 跳转到课程页面
        router.push('/student/courses')
      }
    }

    onMounted(() => {
      console.log('学习进度页面已加载')
    })

    return {
      timeRange,
      sortBy,
      overallProgress,
      completedLessons,
      studyHours,
      achievements,
      courses,
      sortedCourses,
      recentAchievements,
      suggestions,
      formatDate,
      continueCourse,
      handleSuggestion
    }
  }
}
</script>
