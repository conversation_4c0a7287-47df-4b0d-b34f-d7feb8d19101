<template>
  <div class="lesson-create-page">
    <div class="page-header">
      <h2 class="page-title">创建教案</h2>
      <p class="page-description">填写教案标题和教学模块，创建您的教学方案</p>
    </div>

    <div class="create-container">
      <BaseCard class="lesson-form-card">
        <form @submit.prevent="handleSubmit" class="lesson-form">
          <div class="form-grid">
            <!-- 基本信息 -->
            <div class="form-section">
              <h3 class="section-title">基本信息</h3>
              <div class="form-row">
                <BaseInput
                  v-model="formData.title"
                  label="教案标题"
                  placeholder="请输入教案标题"
                  required
                  :error="!!errors.title"
                  :error-message="errors.title"
                  maxlength="100"
                  show-char-count
                />
              </div>
            </div>

            <!-- 教学模块 -->
            <div class="form-section">
              <div class="section-header">
                <h3 class="section-title">教学模块</h3>
                <BaseButton
                  variant="outline"
                  size="sm"
                  @click="addModule"
                  :disabled="isSubmitting"
                >
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="button-icon">
                    <path d="M12 5V19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  添加模块
                </BaseButton>
              </div>

              <div v-if="errors.modules" class="error-message">
                {{ errors.modules }}
              </div>

              <div v-if="modules.length === 0" class="empty-modules">
                <p>还没有添加教学模块，点击"添加模块"开始创建</p>
              </div>

              <div v-else class="modules-list">
                <div
                  v-for="(module, index) in modules"
                  :key="module.id"
                  class="module-item"
                >
                  <div class="module-header">
                    <span class="module-number">模块 {{ index + 1 }}</span>
                    <div class="module-actions">
                      <BaseButton
                        variant="ghost"
                        size="sm"
                        @click="moveModuleUp(index)"
                        :disabled="index === 0 || isSubmitting"
                        title="上移"
                      >
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="button-icon">
                          <path d="M18 15L12 9L6 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </BaseButton>
                      <BaseButton
                        variant="ghost"
                        size="sm"
                        @click="moveModuleDown(index)"
                        :disabled="index === modules.length - 1 || isSubmitting"
                        title="下移"
                      >
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="button-icon">
                          <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </BaseButton>
                      <BaseButton
                        variant="ghost"
                        size="sm"
                        @click="removeModule(index)"
                        :disabled="isSubmitting"
                        title="删除"
                        class="delete-button"
                      >
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="button-icon">
                          <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </BaseButton>
                    </div>
                  </div>

                  <div class="module-content">
                    <div class="form-row">
                      <BaseInput
                        v-model="module.title"
                        label="模块标题"
                        placeholder="请输入模块标题"
                        required
                        :error="!!errors[`module_${index}_title`]"
                        :error-message="errors[`module_${index}_title`]"
                        maxlength="100"
                        show-char-count
                      />
                    </div>

                    <div class="form-row">
                      <BaseInput
                        v-model="module.content"
                        type="textarea"
                        label="模块内容"
                        placeholder="请输入模块的详细内容..."
                        rows="4"
                        :error="!!errors[`module_${index}_content`]"
                        :error-message="errors[`module_${index}_content`]"
                        maxlength="2000"
                        show-char-count
                      />
                    </div>

                    <div class="form-row">
                      <BaseInput
                        v-model="module.fileUrl"
                        label="附件链接（可选）"
                        placeholder="如：https://example.com/file.pdf"
                        type="url"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 表单操作按钮 -->
          <div class="form-actions">
            <BaseButton
              variant="ghost"
              size="lg"
              @click="handleReset"
              :disabled="isSubmitting"
            >
              重置
            </BaseButton>
            <BaseButton
              variant="outline"
              size="lg"
              @click="handleSaveDraft"
              :loading="isSavingDraft"
              :disabled="isSubmitting"
            >
              保存草稿
            </BaseButton>
            <BaseButton
              type="submit"
              variant="primary"
              size="lg"
              :loading="isSubmitting"
            >
              创建教案
            </BaseButton>
          </div>
        </form>
      </BaseCard>
    </div>

    <!-- 成功提示模态框 -->
    <BaseModal
      v-model="showSuccessModal"
      title="创建成功"
      size="sm"
      :closable="true"
    >
      <div class="success-content">
        <div class="success-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M22 11.08V12C21.9988 14.1564 21.3005 16.2547 20.0093 17.9818C18.7182 19.7088 16.9033 20.9725 14.8354 21.5839C12.7674 22.1953 10.5573 22.1219 8.53447 21.3746C6.51168 20.6273 4.78465 19.2461 3.61096 17.4371C2.43727 15.628 1.87979 13.4905 2.02168 11.3363C2.16356 9.18203 2.99721 7.13214 4.39828 5.49883C5.79935 3.86553 7.69279 2.72636 9.79619 2.24223C11.8996 1.75809 14.1003 1.95185 16.07 2.79999" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M22 4L12 14.01L9 11.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <p>教案创建成功！您可以在教案列表中查看和管理您的教案。</p>
      </div>
      <template #footer>
        <BaseButton
          variant="outline"
          @click="goToLessonList"
        >
          查看列表
        </BaseButton>
        <BaseButton
          variant="primary"
          @click="createAnother"
        >
          继续创建
        </BaseButton>
      </template>
    </BaseModal>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import BaseInput from '@/components/common/BaseInput.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseModal from '@/components/common/BaseModal.vue'
import { createLessonPlan } from '@/api/lessonPlan'
import { getCurrentUser } from '@/api/auth'
import { showSuccess, showError } from '@/utils/notification'

export default {
  name: 'LessonCreatePage',
  components: {
    BaseInput,
    BaseButton,
    BaseCard,
    BaseModal
  },
  setup() {
    const router = useRouter()

    const formData = reactive({
      title: ''
    })

    // 教学模块管理
    const modules = ref([
      {
        id: 1,
        title: '',
        content: '',
        fileUrl: ''
      }
    ])
    let moduleIdCounter = 1

    const errors = reactive({})
    const isSubmitting = ref(false)
    const isSavingDraft = ref(false)
    const showSuccessModal = ref(false)

    // 模块管理函数
    const addModule = () => {
      modules.value.push({
        id: ++moduleIdCounter,
        title: '',
        content: '',
        fileUrl: ''
      })
    }

    const removeModule = (index) => {
      modules.value.splice(index, 1)
      // 清除相关的错误信息
      Object.keys(errors).forEach(key => {
        if (key.startsWith(`module_${index}_`)) {
          delete errors[key]
        }
      })
    }

    const moveModuleUp = (index) => {
      if (index > 0) {
        const temp = modules.value[index]
        modules.value[index] = modules.value[index - 1]
        modules.value[index - 1] = temp
      }
    }

    const moveModuleDown = (index) => {
      if (index < modules.value.length - 1) {
        const temp = modules.value[index]
        modules.value[index] = modules.value[index + 1]
        modules.value[index + 1] = temp
      }
    }

    const validateForm = () => {
      const newErrors = {}

      // 基本信息验证
      if (!formData.title.trim()) {
        newErrors.title = '请输入教案标题'
      }

      // 模块验证
      if (modules.value.length === 0) {
        newErrors.modules = '请至少添加一个教学模块'
      } else {
        modules.value.forEach((module, index) => {
          if (!module.title.trim()) {
            newErrors[`module_${index}_title`] = '请输入模块标题'
          }
          if (!module.content.trim()) {
            newErrors[`module_${index}_content`] = '请输入模块内容'
          }
        })
      }

      Object.assign(errors, newErrors)
      return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async () => {
      if (!validateForm()) {
        return
      }

      isSubmitting.value = true

      try {
        // 获取当前用户信息
        const currentUser = getCurrentUser()
        if (!currentUser) {
          showError('用户未登录', '请先登录后再创建教案')
          return
        }

        console.log('当前用户信息:', currentUser)

        // 构建API数据结构 - 按照后端期望的完整格式
        const apiData = {
          createBy: currentUser.username || '',
          createTime: '',
          updateBy: '',
          updateTime: '',
          remark: '',
          params: {},
          id: 0,
          title: formData.title.trim(),
          createUser: parseInt(currentUser.id) || parseInt(currentUser.userId) || 1, // 使用数字类型的用户ID，默认为1
          tpModuleList: modules.value.map((module, index) => ({
            createBy: currentUser.username || '',
            createTime: '',
            updateBy: '',
            updateTime: '',
            remark: '',
            params: {},
            id: 0,
            title: module.title.trim(),
            planId: 0,
            sort: index + 1,
            content: {
              createBy: currentUser.username || '',
              createTime: '',
              updateBy: '',
              updateTime: '',
              remark: '',
              params: {},
              id: 0,
              moduleId: 0,
              content: module.content.trim(), // 注意这里是content而不是text
              fileUrl: module.fileUrl.trim() || ''
            }
          }))
        }

        console.log('创建教案数据:', apiData)

        // 调用API创建教案
        const response = await createLessonPlan(apiData)

        console.log('创建教案成功:', response)
        showSuccess('创建成功', '教案已成功创建！')
        showSuccessModal.value = true
      } catch (error) {
        console.error('创建教案失败:', error)

        // 处理不同类型的错误
        if (error.response) {
          const status = error.response.status
          const message = error.response.data?.msg || error.response.data?.message || '创建失败'

          if (status === 401) {
            showError('认证失败', '请重新登录后再试')
          } else if (status === 400) {
            showError('数据验证失败', message)
          } else {
            showError('创建失败', message)
          }
        } else if (error.message) {
          showError('创建失败', error.message)
        } else {
          showError('创建失败', '网络错误，请稍后重试')
        }
      } finally {
        isSubmitting.value = false
      }
    }

    const handleSaveDraft = async () => {
      isSavingDraft.value = true

      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        console.log('保存草稿:', formData)
        // 显示保存成功提示
      } catch (error) {
        console.error('保存草稿失败:', error)
      } finally {
        isSavingDraft.value = false
      }
    }

    const handleReset = () => {
      formData.title = ''
      modules.value = [{
        id: 1,
        title: '',
        content: '',
        fileUrl: ''
      }]
      moduleIdCounter = 1
      Object.keys(errors).forEach(key => {
        delete errors[key]
      })
    }

    const goToLessonList = () => {
      showSuccessModal.value = false
      router.push('/teacher/lesson-list')
    }

    const createAnother = () => {
      showSuccessModal.value = false
      handleReset()
    }

    return {
      formData,
      modules,
      errors,
      isSubmitting,
      isSavingDraft,
      showSuccessModal,
      addModule,
      removeModule,
      moveModuleUp,
      moveModuleDown,
      handleSubmit,
      handleSaveDraft,
      handleReset,
      goToLessonList,
      createAnother
    }
  }
}
</script>

<style scoped>
.lesson-create-page {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-description {
  font-size: var(--text-lg);
  color: var(--text-secondary);
}

.create-container {
  margin-bottom: var(--spacing-xl);
}

.lesson-form-card {
  padding: 0;
}

.lesson-form {
  padding: var(--spacing-xl);
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--primary-100);
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.form-row-2 {
  flex-direction: row;
  gap: var(--spacing-lg);
}

.form-row-2 > * {
  flex: 1;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
  margin-top: var(--spacing-xl);
}

.success-content {
  text-align: center;
  padding: var(--spacing-lg) 0;
}

.success-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-lg);
  color: var(--success-color);
}

.success-content p {
  font-size: var(--text-base);
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 教学模块样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.button-icon {
  width: 16px;
  height: 16px;
}

.error-message {
  color: var(--error-color);
  font-size: var(--text-sm);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--error-50);
  border: 1px solid var(--error-200);
  border-radius: var(--radius-sm);
}

.empty-modules {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 2px dashed var(--border-color);
}

.modules-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.module-item {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--white);
  overflow: hidden;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--gray-50);
  border-bottom: 1px solid var(--border-color);
}

.module-number {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.module-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.delete-button {
  color: var(--error-color);
}

.delete-button:hover {
  background: var(--error-50);
  color: var(--error-600);
}

.module-content {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lesson-form {
    padding: var(--spacing-lg);
  }

  .form-row-2 {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: var(--spacing-sm);
  }

  .form-actions > * {
    width: 100%;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .module-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .module-actions {
    justify-content: center;
  }

  .module-content {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .lesson-form {
    padding: var(--spacing-md);
  }
  
  .form-grid {
    gap: var(--spacing-xl);
  }
  
  .form-section {
    gap: var(--spacing-md);
  }
}
</style>
