<template>
  <div class="lesson-edit-page">
    <div class="page-header">
      <h2 class="page-title">编辑教案</h2>
      <p class="page-description">修改教案标题和教学模块，更新您的教学方案</p>
    </div>

    <div class="edit-container">
      <BaseCard class="lesson-form-card">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>正在加载教案数据...</p>
        </div>
        
        <form v-else @submit.prevent="handleSubmit" class="lesson-form">
          <div class="form-grid">
            <!-- 基本信息 -->
            <div class="form-section">
              <h3 class="section-title">基本信息</h3>
              <div class="form-row">
                <BaseInput
                  v-model="formData.title"
                  label="教案标题"
                  placeholder="请输入教案标题"
                  required
                  :error="!!errors.title"
                  :error-message="errors.title"
                  maxlength="100"
                  show-char-count
                />
              </div>
            </div>

            <!-- 教学模块 -->
            <div class="form-section">
              <div class="section-header">
                <h3 class="section-title">教学模块</h3>
                <BaseButton
                  type="button"
                  variant="outline"
                  size="sm"
                  @click="addModule"
                >
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 5V19M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  添加模块
                </BaseButton>
              </div>

              <div class="modules-container">
                <div
                  v-for="(module, index) in modules"
                  :key="module.id"
                  class="module-item"
                >
                  <div class="module-header">
                    <h4 class="module-title">模块 {{ index + 1 }}</h4>
                    <BaseButton
                      v-if="modules.length > 1"
                      type="button"
                      variant="danger"
                      size="sm"
                      @click="removeModule(index)"
                    >
                      <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                      删除
                    </BaseButton>
                  </div>

                  <div class="module-content">
                    <div class="form-row">
                      <BaseInput
                        v-model="module.title"
                        label="模块标题"
                        placeholder="请输入模块标题"
                        required
                        :error="!!errors[`module_${index}_title`]"
                        :error-message="errors[`module_${index}_title`]"
                        maxlength="100"
                        show-char-count
                      />
                    </div>

                    <div class="form-row">
                      <BaseInput
                        v-model="module.content"
                        type="textarea"
                        label="模块内容"
                        placeholder="请输入模块的详细内容..."
                        required
                        :error="!!errors[`module_${index}_content`]"
                        :error-message="errors[`module_${index}_content`]"
                        rows="6"
                        maxlength="2000"
                        show-char-count
                      />
                    </div>

                    <div class="form-row">
                      <BaseInput
                        v-model="module.fileUrl"
                        label="附件链接（可选）"
                        placeholder="请输入相关文件的URL链接"
                        :error="!!errors[`module_${index}_fileUrl`]"
                        :error-message="errors[`module_${index}_fileUrl`]"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 表单操作 -->
          <div class="form-actions">
            <BaseButton
              type="button"
              variant="outline"
              @click="handleCancel"
              :disabled="isSubmitting"
            >
              取消
            </BaseButton>
            <BaseButton
              type="submit"
              variant="primary"
              :loading="isSubmitting"
              :disabled="isSubmitting"
            >
              {{ isSubmitting ? '更新中...' : '更新教案' }}
            </BaseButton>
          </div>
        </form>
      </BaseCard>
    </div>

    <!-- 成功提示模态框 -->
    <BaseModal
      v-model="showSuccessModal"
      title="更新成功"
      size="sm"
    >
      <div class="success-content">
        <div class="success-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <p>教案已成功更新！</p>
      </div>
      <template #footer>
        <BaseButton
          variant="primary"
          @click="goToLessonList"
        >
          返回教案列表
        </BaseButton>
      </template>
    </BaseModal>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseInput from '@/components/common/BaseInput.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import BaseModal from '@/components/common/BaseModal.vue'
import { getLessonPlanById, updateLessonPlan } from '@/api/lessonPlan'
import { getCurrentUser } from '@/api/auth'
import { showSuccess, showError } from '@/utils/notification'

export default {
  name: 'LessonEditPage',
  components: {
    BaseCard,
    BaseInput,
    BaseButton,
    BaseModal
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const planId = route.params.id

    const loading = ref(true)
    const formData = reactive({
      title: ''
    })

    // 原始教案数据，用于保留审计字段
    const originalPlanData = ref(null)

    // 教学模块管理
    const modules = ref([])
    let moduleIdCounter = 1

    const errors = reactive({})
    const isSubmitting = ref(false)
    const showSuccessModal = ref(false)

    // 日期格式化函数 - 转换为后端期望的格式
    const formatDateForBackend = (date) => {
      if (!date) return ''

      // 如果已经是正确格式的字符串，直接返回
      if (typeof date === 'string' && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(date)) {
        return date
      }

      // 转换为 yyyy-MM-dd HH:mm:ss 格式
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      const hours = String(d.getHours()).padStart(2, '0')
      const minutes = String(d.getMinutes()).padStart(2, '0')
      const seconds = String(d.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }

    // 加载教案数据
    const loadLessonPlan = async () => {
      try {
        loading.value = true
        console.log('加载教案数据，ID:', planId)
        
        const response = await getLessonPlanById(planId)
        console.log('获取教案数据成功:', response)
        
        // 保存原始数据
        originalPlanData.value = response.data || response
        
        // 填充表单数据
        const planData = originalPlanData.value
        formData.title = planData.title || ''
        
        // 处理模块数据
        if (planData.tpModuleList && planData.tpModuleList.length > 0) {
          modules.value = planData.tpModuleList.map((module, index) => ({
            id: module.id || (++moduleIdCounter),
            title: module.title || '',
            content: module.content?.content || '',
            fileUrl: module.content?.fileUrl || '',
            originalData: module // 保存原始模块数据
          }))
          moduleIdCounter = Math.max(moduleIdCounter, ...modules.value.map(m => m.id))
        } else {
          // 如果没有模块，创建一个空模块
          modules.value = [{
            id: ++moduleIdCounter,
            title: '',
            content: '',
            fileUrl: ''
          }]
        }
        
      } catch (error) {
        console.error('加载教案数据失败:', error)
        showError('加载失败', '无法加载教案数据，请稍后重试')
        router.push('/teacher/lesson-list')
      } finally {
        loading.value = false
      }
    }

    // 模块管理函数
    const addModule = () => {
      modules.value.push({
        id: ++moduleIdCounter,
        title: '',
        content: '',
        fileUrl: ''
      })
    }

    const removeModule = (index) => {
      modules.value.splice(index, 1)
      // 清除相关的错误信息
      Object.keys(errors).forEach(key => {
        if (key.startsWith(`module_${index}_`)) {
          delete errors[key]
        }
      })
    }

    const validateForm = () => {
      const newErrors = {}

      // 基本信息验证
      if (!formData.title.trim()) {
        newErrors.title = '请输入教案标题'
      }

      // 模块验证
      if (modules.value.length === 0) {
        newErrors.modules = '请至少添加一个教学模块'
      } else {
        modules.value.forEach((module, index) => {
          if (!module.title.trim()) {
            newErrors[`module_${index}_title`] = '请输入模块标题'
          }
          if (!module.content.trim()) {
            newErrors[`module_${index}_content`] = '请输入模块内容'
          }
        })
      }

      Object.assign(errors, newErrors)
      return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async () => {
      if (!validateForm()) {
        return
      }

      isSubmitting.value = true

      try {
        // 获取当前用户信息
        const currentUser = getCurrentUser()
        if (!currentUser) {
          showError('用户未登录', '请先登录后再更新教案')
          return
        }

        console.log('当前用户信息:', currentUser)
        console.log('原始教案数据:', originalPlanData.value)

        // 构建API数据结构 - 保留原有字段并更新必要信息
        const apiData = {
          // 保留原有的审计字段
          createBy: originalPlanData.value.createBy || '',
          createTime: formatDateForBackend(originalPlanData.value.createTime) || '',
          updateBy: currentUser.username || '',
          updateTime: formatDateForBackend(new Date()),
          remark: originalPlanData.value.remark || '',
          params: originalPlanData.value.params || {},
          
          // 必须包含ID
          id: originalPlanData.value.id,
          title: formData.title.trim(),
          createUser: originalPlanData.value.createUser,
          
          tpModuleList: modules.value.map((module, index) => ({
            // 保留原有模块的审计字段（如果存在）
            createBy: module.originalData?.createBy || currentUser.username || '',
            createTime: formatDateForBackend(module.originalData?.createTime) || '',
            updateBy: currentUser.username || '',
            updateTime: formatDateForBackend(new Date()),
            remark: module.originalData?.remark || '',
            params: module.originalData?.params || {},
            
            id: module.originalData?.id || 0, // 新模块ID为0，让后端分配
            title: module.title.trim(),
            planId: originalPlanData.value.id,
            sort: index + 1,
            content: {
              // 保留原有内容的审计字段（如果存在）
              createBy: module.originalData?.content?.createBy || currentUser.username || '',
              createTime: formatDateForBackend(module.originalData?.content?.createTime) || '',
              updateBy: currentUser.username || '',
              updateTime: formatDateForBackend(new Date()),
              remark: module.originalData?.content?.remark || '',
              params: module.originalData?.content?.params || {},
              
              id: module.originalData?.content?.id || 0,
              moduleId: module.originalData?.id || 0,
              content: module.content.trim(),
              fileUrl: module.fileUrl.trim() || ''
            }
          }))
        }

        console.log('更新教案数据:', apiData)

        // 调用API更新教案
        const response = await updateLessonPlan(apiData)

        console.log('更新教案成功:', response)
        showSuccess('更新成功', '教案已成功更新！')
        showSuccessModal.value = true
      } catch (error) {
        console.error('更新教案失败:', error)

        // 处理不同类型的错误
        if (error.response) {
          const status = error.response.status
          const message = error.response.data?.msg || error.response.data?.message || '更新失败'

          if (status === 401) {
            showError('认证失败', '请重新登录后再试')
          } else if (status === 400) {
            showError('数据验证失败', message)
          } else {
            showError('更新失败', message)
          }
        } else if (error.message) {
          showError('更新失败', error.message)
        } else {
          showError('更新失败', '网络错误，请稍后重试')
        }
      } finally {
        isSubmitting.value = false
      }
    }

    const handleCancel = () => {
      router.push('/teacher/lesson-list')
    }

    const goToLessonList = () => {
      showSuccessModal.value = false
      router.push('/teacher/lesson-list')
    }

    // 页面加载时获取教案数据
    onMounted(() => {
      if (!planId) {
        showError('参数错误', '缺少教案ID参数')
        router.push('/teacher/lesson-list')
        return
      }
      loadLessonPlan()
    })

    return {
      loading,
      formData,
      modules,
      errors,
      isSubmitting,
      showSuccessModal,
      addModule,
      removeModule,
      handleSubmit,
      handleCancel,
      goToLessonList
    }
  }
}
</script>

<style scoped>
.lesson-edit-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-description {
  font-size: var(--text-lg);
  color: var(--text-secondary);
}

.edit-container {
  width: 100%;
}

.lesson-form-card {
  padding: var(--spacing-xl);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.lesson-form {
  width: 100%;
}

.form-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.form-section {
  background: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
}

.section-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.form-row {
  margin-bottom: var(--spacing-lg);
}

.form-row:last-child {
  margin-bottom: 0;
}

.modules-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.module-item {
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.module-title {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.module-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-lg);
}

.success-icon {
  width: 64px;
  height: 64px;
  color: var(--success-color);
  margin-bottom: var(--spacing-md);
}

.success-icon svg {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .lesson-edit-page {
    padding: var(--spacing-md);
  }

  .lesson-form-card {
    padding: var(--spacing-lg);
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .form-actions {
    flex-direction: column;
  }

  .module-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .form-section {
    background: var(--surface-dark);
    border-color: var(--border-dark);
  }

  .module-item {
    background: var(--background-dark);
    border-color: var(--border-dark);
  }

  .module-header {
    border-bottom-color: var(--border-dark);
  }

  .form-actions {
    border-top-color: var(--border-dark);
  }
}
</style>
