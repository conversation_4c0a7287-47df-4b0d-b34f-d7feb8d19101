<template>
  <div class="lesson-generator-page">
    <div class="generator-container">
      <!-- 对话历史区域 -->
      <div class="chat-history" ref="chatHistoryRef">
        <div v-if="messages.length === 0" class="empty-state">
          <div class="empty-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 2V5" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M16 2V5" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <rect x="3" y="4" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2"/>
              <path d="M3 10H21" stroke="currentColor" stroke-width="2"/>
              <path d="M8 14H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M8 18H12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </div>
          <h3>开始生成您的教案</h3>
          <p>请输入您想要生成教案的主题，AI将为您创建详细的教学方案</p>
          <div class="example-topics">
            <h4>示例主题：</h4>
            <div class="topic-tags">
              <span class="topic-tag" @click="selectTopic('小学数学：分数的认识')">小学数学：分数的认识</span>
              <span class="topic-tag" @click="selectTopic('初中语文：古诗词鉴赏')">初中语文：古诗词鉴赏</span>
              <span class="topic-tag" @click="selectTopic('高中物理：牛顿运动定律')">高中物理：牛顿运动定律</span>
            </div>
          </div>
        </div>

        <div v-else class="messages">
          <div 
            v-for="(message, index) in messages" 
            :key="index" 
            class="message"
            :class="{ 'message-user': message.role === 'user', 'message-ai': message.role === 'ai' }"
          >
            <div class="message-avatar">
              <svg v-if="message.role === 'user'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
              </svg>
              <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="message-content">
              <div class="message-text" v-html="formatMessage(message.content)"></div>

              <!-- 教案操作按钮 -->
              <div v-if="message.role === 'ai' && isLessonPlanContent(message.content)" class="lesson-actions">
                <button
                  @click="createLessonFromAI(message.content)"
                  class="create-lesson-btn"
                  :disabled="creatingLesson"
                >
                  <svg v-if="!creatingLesson" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M16 13H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M16 17H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M10 9H9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg v-else class="loading-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
                    <path d="M12 3C16.9706 3 21 7.02944 21 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                  {{ creatingLesson ? '创建中...' : '一键创建为教案' }}
                </button>

                <button
                  @click="downloadAsWord(message.content)"
                  class="download-word-btn"
                  :disabled="downloadingWord"
                >
                  <svg v-if="!downloadingWord" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <svg v-else class="loading-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
                    <path d="M12 3C16.9706 3 21 7.02944 21 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                  {{ downloadingWord ? '生成中...' : '下载为Word' }}
                </button>
              </div>

              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="message message-ai">
            <div class="message-avatar">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="message-content">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <div class="input-container">
          <textarea
            v-model="inputMessage"
            placeholder="请输入您想要生成教案的主题或具体要求..."
            class="message-input"
            rows="3"
            @keydown.enter.exact.prevent="sendMessage"
            @keydown.enter.shift.exact="addNewLine"
            :disabled="loading"
          ></textarea>
          <button 
            class="send-button"
            @click="sendMessage"
            :disabled="!inputMessage.trim() || loading"
          >
            <svg v-if="!loading" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22 2L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg v-else class="loading-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
              <path d="M12 3C16.9706 3 21 7.02944 21 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
        <div class="input-tips">
          <span class="tip">按 Enter 发送，Shift + Enter 换行</span>
          <span v-if="loading" class="status">{{ connectionStatus }}</span>
          <button v-if="lastFailedMessage" @click="retryLastMessage" class="retry-button">
            重试上一条消息
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useChatNonStream } from '@/composables/useChatNonStream'
import { createLessonPlan } from '@/api/lessonPlan'
import { getCurrentUser } from '@/api/auth'
import { setUser } from '@/utils/auth'
import { isLessonPlanContent, parseLessonPlan, convertToApiFormat, validateParsedData } from '@/utils/lessonParser'
import { downloadLessonPlanAsWord, isWordDownloadSupported } from '@/utils/wordGenerator'

export default {
  name: 'LessonGeneratorPage',
  setup() {
    const router = useRouter()
    const inputMessage = ref('')
    const chatHistoryRef = ref(null)
    const creatingLesson = ref(false)
    const downloadingWord = ref(false)

    // 使用教案生成专用的BOT_ID
    const lessonBotId = import.meta.env.VITE_COZE_LESSON_BOT_ID

    // 使用非流式聊天处理，传入教案专用BOT_ID
    const {
      messages,
      conversationId,
      lastFailedMessage,
      connectionStatus,
      loading,
      sendMessage: sendChatMessage,
      retryLastMessage
    } = useChatNonStream(lessonBotId)

    const selectTopic = (topic) => {
      inputMessage.value = topic
    }

    const sendMessage = async () => {
      if (!inputMessage.value.trim() || loading.value) return

      const messageToSend = inputMessage.value.trim()
      inputMessage.value = ''

      await sendChatMessage(messageToSend)
      await scrollToBottom()
    }



    const formatMessage = (content) => {
      if (!content) return '';
      
      // Process markdown images first
      let processed = content.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (match, alt, url) => {
        return `<img src="${url}" alt="${alt}" class="message-image">`;
      });
      
      // Handle markdown bold text (**text**)
      processed = processed.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      
      // Handle markdown italic text (*text*)
      processed = processed.replace(/\*([^\*]+)\*/g, '<em>$1</em>');
      
      // Then handle line breaks
      return processed.replace(/\n/g, '<br>');
    }

    const formatTime = (timestamp) => {
      const date = new Date(timestamp)
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const scrollToBottom = async () => {
      await nextTick()
      if (chatHistoryRef.value) {
        chatHistoryRef.value.scrollTop = chatHistoryRef.value.scrollHeight
      }
    }

    const addNewLine = () => {
      inputMessage.value += '\n'
    }

    // 创建教案功能
    const createLessonFromAI = async (content) => {
      if (creatingLesson.value) return

      try {
        creatingLesson.value = true

        // 获取当前用户信息
        let currentUser = getCurrentUser()
        console.log('当前用户信息:', currentUser)

        // 如果没有用户信息，创建一个测试用户
        if (!currentUser) {
          console.log('未找到用户信息，创建测试用户')
          const testUser = {
            id: 1,
            userId: 1,
            username: 'admin',
            role: 'admin',
            email: '<EMAIL>'
          }
          setUser(testUser)
          currentUser = testUser
          console.log('使用测试用户:', currentUser)
        }

        // 解析AI回复内容
        console.log('开始解析教案内容...')
        const parsedData = parseLessonPlan(content)
        console.log('解析结果:', parsedData)

        // 验证解析结果
        const validation = validateParsedData(parsedData)
        if (!validation.isValid) {
          alert(`教案内容解析失败：${validation.errors.join(', ')}`)
          return
        }

        // 转换为API格式
        const apiData = convertToApiFormat(parsedData, currentUser)
        console.log('API数据:', apiData)

        // 调用API创建教案
        const response = await createLessonPlan(apiData)
        console.log('创建教案成功:', response)

        // 显示成功提示
        if (confirm('教案创建成功！是否跳转到教案列表查看？')) {
          router.push('/teacher/lesson-list')
        }

      } catch (error) {
        console.error('创建教案失败:', error)

        let errorMessage = '创建教案失败'
        if (error.message) {
          errorMessage += `：${error.message}`
        } else if (error.response && error.response.data && error.response.data.msg) {
          errorMessage += `：${error.response.data.msg}`
        }

        alert(errorMessage)
      } finally {
        creatingLesson.value = false
      }
    }

    // 下载为Word文档
    const downloadAsWord = async (content) => {
      if (!content || downloadingWord.value) return

      // 检查浏览器支持
      if (!isWordDownloadSupported()) {
        alert('您的浏览器不支持Word文档下载功能')
        return
      }

      downloadingWord.value = true

      try {
        await downloadLessonPlanAsWord(content)
        console.log('Word文档下载成功')
      } catch (error) {
        console.error('下载Word文档失败:', error)

        let errorMessage = '下载Word文档失败'
        if (error.message) {
          errorMessage += `：${error.message}`
        }

        alert(errorMessage)
      } finally {
        downloadingWord.value = false
      }
    }

    // 监听消息变化，自动滚动到底部
    watch(messages, async () => {
      await scrollToBottom()
    }, { deep: true })

    return {
      messages,
      inputMessage,
      loading,
      chatHistoryRef,
      connectionStatus,
      creatingLesson,
      downloadingWord,
      selectTopic,
      sendMessage,
      formatMessage,
      formatTime,
      addNewLine,
      retryLastMessage,
      lastFailedMessage,
      isLessonPlanContent,
      createLessonFromAI,
      downloadAsWord
    }
  }
}
</script>

<style scoped>
.lesson-generator-page {
  max-width: 1200px;
  margin: 0 auto;
}

.generator-container {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  height: 85vh;
  display: flex;
  flex-direction: column;
}

.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  width: 80px;
  height: 80px;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-state p {
  font-size: var(--text-base);
  margin-bottom: var(--spacing-xl);
}

.example-topics h4 {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.topic-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
}

.topic-tag {
  background-color: var(--primary-50);
  color: var(--primary-700);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.topic-tag:hover {
  background-color: var(--primary-100);
  transform: translateY(-1px);
}

.messages {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.message {
  display: flex;
  gap: var(--spacing-sm);
  align-items: flex-start;
}

.message-user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.message-user .message-avatar {
  background-color: var(--primary-100);
  color: var(--primary-600);
}

.message-ai .message-avatar {
  background-color: var(--secondary-color);
  color: white;
}

.message-avatar svg {
  width: 20px;
  height: 20px;
}

.message-content {
  max-width: 70%;
  background-color: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  position: relative;
}

.message-user .message-content {
  background-color: var(--primary-500);
  color: white;
}

.message-text {
  line-height: 1.6;
  word-wrap: break-word;
}

.message-text h3 {
  margin-bottom: var(--spacing-sm);
  color: inherit;
}

.message-text h4 {
  margin: var(--spacing-md) 0 var(--spacing-xs) 0;
  color: inherit;
}

.message-text ul, .message-text ol {
  margin: var(--spacing-sm) 0;
  padding-left: var(--spacing-lg);
}

.message-text li {
  margin-bottom: var(--spacing-xs);
}

.message-image {
  max-width: 100%;
  border-radius: var(--radius-md);
  margin: var(--spacing-sm) 0;
}

.message-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
}

.message-user .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--text-secondary);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.input-area {
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-lg);
}

.input-container {
  display: flex;
  gap: var(--spacing-sm);
  align-items: flex-end;
}

.message-input {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  font-size: var(--text-base);
  line-height: 1.5;
  resize: none;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-fast);
}

.message-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px;
  height: 48px;
}

.send-button:hover:not(:disabled) {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

.send-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.send-button svg {
  width: 20px;
  height: 20px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.input-tips {
  margin-top: var(--spacing-sm);
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-md);
}

.tip {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.status {
  font-size: var(--text-xs);
  color: var(--primary-color);
  font-weight: var(--font-medium);
}

.retry-button {
  font-size: var(--text-xs);
  color: var(--primary-color);
  background: none;
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.retry-button:hover {
  background-color: var(--primary-color);
  color: white;
}

/* 教案操作按钮样式 */
.lesson-actions {
  margin: 1rem 0 0.5rem 0;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.create-lesson-btn,
.download-word-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 140px;
  justify-content: center;
}

.create-lesson-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.2);
}

.download-word-btn {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.create-lesson-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(var(--primary-rgb), 0.3);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.download-word-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
  background: linear-gradient(135deg, #20c997, #17a2b8);
}

.create-lesson-btn:active:not(:disabled),
.download-word-btn:active:not(:disabled) {
  transform: translateY(0);
}

.create-lesson-btn:active:not(:disabled) {
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.2);
}

.download-word-btn:active:not(:disabled) {
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.create-lesson-btn:disabled,
.download-word-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.create-lesson-btn svg,
.download-word-btn svg {
  width: 1.125rem;
  height: 1.125rem;
  flex-shrink: 0;
}

.create-lesson-btn .loading-icon,
.download-word-btn .loading-icon {
  animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .generator-container {
    height: 75vh;
  }

  .message-content {
    max-width: 85%;
  }

  .topic-tags {
    flex-direction: column;
    align-items: center;
  }

  .lesson-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .create-lesson-btn,
  .download-word-btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.8125rem;
    min-width: auto;
  }

  .create-lesson-btn svg,
  .download-word-btn svg {
    width: 1rem;
    height: 1rem;
  }
}
</style>
