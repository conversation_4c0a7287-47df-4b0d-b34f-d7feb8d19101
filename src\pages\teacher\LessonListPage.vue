<template>
  <div class="lesson-list-page">
    <div class="page-header">
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters-section">
      <BaseCard class="filters-card">
        <div class="filters-content">
          <div class="search-row">
            <BaseInput
              v-model="searchQuery"
              placeholder="搜索教案标题、学科或年级..."
              size="lg"
              clearable
              @input="debounceSearch"
            >
              <template #prefix-icon>
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                  <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </template>
              <template #suffix>
                <BaseButton 
                  variant="primary" 
                  size="sm" 
                  class="search-btn"
                  @click="handleSearch">
                  搜索
                </BaseButton>
              </template>
            </BaseInput>
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- 教案列表 -->
    <div class="lessons-section">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
            <path d="M12 3C16.9706 3 21 7.02944 21 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </div>
        <p>加载中...</p>
      </div>

      <div v-else-if="filteredLessons.length === 0" class="empty-state">
        <div class="empty-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h3>暂无教案</h3>
        <p>您还没有创建任何教案，点击上方按钮开始创建吧！</p>
        <BaseButton
          variant="primary"
          size="lg"
          @click="goToCreate"
          class="empty-action"
        >
          创建第一个教案
        </BaseButton>
      </div>

      <div v-else class="lessons-grid">
        <BaseCard
          v-for="lesson in paginatedLessons"
          :key="lesson.id"
          class="lesson-card"
          hoverable
          clickable
          @click="viewLesson(lesson)"
        >
          <template #header>
            <div class="lesson-header">
              <div class="lesson-meta">
              </div>
              <div class="lesson-actions" @click.stop>
                <button class="action-btn" @click="editLesson(lesson)" title="编辑">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M18.5 2.50023C18.8978 2.1024 19.4374 1.87891 20 1.87891C20.5626 1.87891 21.1022 2.1024 21.5 2.50023C21.8978 2.89805 22.1213 3.43762 22.1213 4.00023C22.1213 4.56284 21.8978 5.1024 21.5 5.50023L12 15.0002L8 16.0002L9 12.0002L18.5 2.50023Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
                <button class="action-btn action-btn-danger" @click="deleteLesson(lesson)" title="删除">
                  <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>
          </template>

          <div class="lesson-content">
            <h3 class="lesson-title">{{ lesson.title }}</h3>
            <p class="lesson-description">{{ lesson.description || '暂无描述' }}</p>
            
            <div class="lesson-stats">
              <div class="stat-item">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <path d="M12 6V12L16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>{{ lesson.duration }}分钟</span>
              </div>
              <div class="stat-item">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 2V5" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  <path d="M16 2V5" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                  <rect x="3" y="4" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2"/>
                  <path d="M3 10H21" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>{{ formatDate(lesson.createdAt) }}</span>
              </div>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="pagination">
        <BaseButton
          variant="outline"
          :disabled="currentPage === 1"
          @click="goToPage(currentPage - 1)"
        >
          上一页
        </BaseButton>
        
        <div class="page-numbers">
          <button
            v-for="page in visiblePages"
            :key="page"
            class="page-btn"
            :class="{ active: page === currentPage }"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
        </div>
        
        <BaseButton
          variant="outline"
          :disabled="currentPage === totalPages"
          @click="goToPage(currentPage + 1)"
        >
          下一页
        </BaseButton>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <BaseModal
      v-model="showDeleteModal"
      title="确认删除"
      size="sm"
    >
      <div class="delete-content">
        <div class="delete-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M15 9L9 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M9 9L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <p>确定要删除教案"{{ selectedLesson?.title }}"吗？</p>
        <p class="delete-warning">此操作不可撤销，请谨慎操作。</p>
      </div>
      <template #footer>
        <BaseButton
          variant="outline"
          @click="showDeleteModal = false"
        >
          取消
        </BaseButton>
        <BaseButton
          variant="danger"
          @click="confirmDelete"
          :loading="isDeleting"
        >
          确认删除
        </BaseButton>
      </template>
    </BaseModal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import BaseInput from '@/components/common/BaseInput.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseModal from '@/components/common/BaseModal.vue'
import { getLessonPlanList, searchLessonPlans, deleteLessonPlan } from '@/api/lessonPlan'

export default {
  name: 'LessonListPage',
  components: {
    BaseInput,
    BaseButton,
    BaseCard,
    BaseModal
  },
  setup() {
    const router = useRouter()
    
    const lessons = ref([])
    const loading = ref(true)
    const searchQuery = ref('')
    const currentPage = ref(1)
    const pageSize = 12
    const showDeleteModal = ref(false)
    const selectedLesson = ref(null)
    const isDeleting = ref(false)

    // 简化后的筛选
    const filters = reactive({
      sortBy: 'createdAt',
      sortOrder: 'desc'
    })

    // 防抖搜索
    let searchTimeout = null
    const debounceSearch = (e) => {
      clearTimeout(searchTimeout)
      searchTimeout = setTimeout(() => {
        handleSearch()
      }, 500)
    }

    // 模拟数据
    const mockLessons = [
      {
        id: 1,
        title: '小学数学：分数的认识',
        subject: '数学',
        grade: '三年级',
        duration: 45,
        description: '通过生活实例让学生理解分数的概念，掌握分数的基本表示方法。',
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-16')
      },
      {
        id: 2,
        title: '初中语文：古诗词鉴赏',
        subject: '语文',
        grade: '初二',
        duration: 50,
        description: '学习古诗词的鉴赏方法，培养学生的文学素养和审美能力。',
        createdAt: new Date('2024-01-14'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: 3,
        title: '高中物理：牛顿运动定律',
        subject: '物理',
        grade: '高一',
        duration: 45,
        description: '深入理解牛顿三大运动定律，掌握其在实际问题中的应用。',
        createdAt: new Date('2024-01-13'),
        updatedAt: new Date('2024-01-14')
      }
    ]

    const filteredLessons = computed(() => {
      let result = [...lessons.value]

      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        result = result.filter(lesson =>
          lesson.title.toLowerCase().includes(query) ||
          lesson.subject.toLowerCase().includes(query) ||
          lesson.grade.toLowerCase().includes(query)
        )
      }

      // 学科过滤
      if (filters.subject) {
        result = result.filter(lesson => lesson.subject === filters.subject)
      }

      // 年级过滤
      if (filters.grade) {
        result = result.filter(lesson => lesson.grade === filters.grade)
      }

      // 排序
      result.sort((a, b) => {
        const aValue = a[filters.sortBy]
        const bValue = b[filters.sortBy]
        
        if (filters.sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1
        } else {
          return aValue < bValue ? 1 : -1
        }
      })

      return result
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredLessons.value.length / pageSize)
    })

    const paginatedLessons = computed(() => {
      const start = (currentPage.value - 1) * pageSize
      const end = start + pageSize
      return filteredLessons.value.slice(start, end)
    })

    const visiblePages = computed(() => {
      const pages = []
      const total = totalPages.value
      const current = currentPage.value
      
      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        } else if (current >= total - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = total - 4; i <= total; i++) {
            pages.push(i)
          }
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        }
      }
      
      return pages
    })

    const loadLessons = async () => {
      loading.value = true
      try {
        // 调用真实的教案列表API
        const response = await getLessonPlanList({
          pageNum: currentPage.value,
          pageSize: pageSize
        })

        // 处理API响应
        if (response && response.rows && Array.isArray(response.rows)) {
          lessons.value = response.rows.map(lesson => ({
            id: lesson.id,
            title: lesson.title || '未命名教案',
            subject: lesson.subject || '未知学科',
            grade: lesson.grade || '未知年级',
            duration: lesson.duration || 45,
            description: lesson.description || lesson.remark || '暂无描述',
            createdAt: lesson.createTime ? new Date(lesson.createTime) : new Date(),
            updatedAt: lesson.updateTime ? new Date(lesson.updateTime) : new Date(),
            createUser: lesson.createUser || lesson.createBy
          }))
          console.log(`成功加载 ${lessons.value.length} 个教案`)
        } else {
          console.warn('API返回数据格式异常，使用模拟数据')
          lessons.value = mockLessons
        }
      } catch (error) {
        console.error('加载教案列表失败:', error)
        // 如果API调用失败，回退到模拟数据
        console.log('回退到模拟数据')
        lessons.value = mockLessons
      } finally {
        loading.value = false
      }
    }

    const handleSearch = async () => {
      currentPage.value = 1
      loading.value = true

      try {
        if (searchQuery.value.trim()) {
          // 使用搜索API
          const response = await searchLessonPlans({
            keyword: searchQuery.value.trim(),
            subject: filters.subject,
            grade: filters.grade,
            pageNum: currentPage.value,
            pageSize: pageSize
          })

          if (response && response.rows && Array.isArray(response.rows)) {
            lessons.value = response.rows.map(lesson => ({
              id: lesson.id,
              title: lesson.title || '未命名教案',
              subject: lesson.subject || '未知学科',
              grade: lesson.grade || '未知年级',
              duration: lesson.duration || 45,
              description: lesson.description || lesson.remark || '暂无描述',
              createdAt: lesson.createTime ? new Date(lesson.createTime) : new Date(),
              updatedAt: lesson.updateTime ? new Date(lesson.updateTime) : new Date(),
              createUser: lesson.createUser || lesson.createBy
            }))
          } else {
            lessons.value = []
          }
        } else {
          // 如果搜索框为空，重新加载所有数据
          await loadLessons()
          return
        }
      } catch (error) {
        console.error('搜索教案失败:', error)
        lessons.value = []
      } finally {
        loading.value = false
      }
    }

    const handleFilter = async () => {
      currentPage.value = 1
      await handleSearch() // 应用筛选时也执行搜索
    }

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
      }
    }

    const goToCreate = () => {
      router.push('/teacher/lesson-create')
    }

    const viewLesson = (lesson) => {
      console.log('查看教案:', lesson)
      // 这里可以跳转到教案详情页
    }

    const editLesson = (lesson) => {
      console.log('编辑教案:', lesson)
      router.push(`/teacher/lesson-edit/${lesson.id}`)
    }

    const deleteLesson = (lesson) => {
      selectedLesson.value = lesson
      showDeleteModal.value = true
    }

    const confirmDelete = async () => {
      if (!selectedLesson.value) return

      isDeleting.value = true
      try {
        // 调用删除API
        await deleteLessonPlan(selectedLesson.value.id)

        // 从本地列表中移除已删除的教案
        const index = lessons.value.findIndex(l => l.id === selectedLesson.value.id)
        if (index > -1) {
          lessons.value.splice(index, 1)
        }

        // 检查当前页是否还有数据，如果没有则回到上一页
        const remainingItems = filteredLessons.value.length
        const maxPage = Math.ceil(remainingItems / pageSize)
        if (currentPage.value > maxPage && maxPage > 0) {
          currentPage.value = maxPage
        }

        console.log(`教案"${selectedLesson.value.title}"删除成功`)
        showDeleteModal.value = false
        selectedLesson.value = null

        // 可以添加成功提示
        // 这里可以使用toast组件替代alert
        // toast.success('教案删除成功')
      } catch (error) {
        console.error('删除教案失败:', error)
        // 这里可以添加错误提示，比如使用toast组件
        alert('删除教案失败，请稍后重试')
      } finally {
        isDeleting.value = false
      }
    }

    const formatDate = (date) => {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    onMounted(() => {
      loadLessons()
    })

    return {
      lessons,
      loading,
      searchQuery,
      filters,
      currentPage,
      totalPages,
      filteredLessons,
      paginatedLessons,
      visiblePages,
      showDeleteModal,
      selectedLesson,
      isDeleting,
      handleSearch,
      handleFilter,
      goToPage,
      goToCreate,
      viewLesson,
      editLesson,
      deleteLesson,
      confirmDelete,
      formatDate
    }
  }
}
</script>

<style scoped>
.lesson-list-page {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-description {
  font-size: var(--text-lg);
  color: var(--text-secondary);
}

.filters-section {
  margin-bottom: var(--spacing-xl);
}

.filters-card {
  padding: 0;
}

.filters-content {
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-row {
  width: 100%;
  position: relative;
}

.search-btn {
  margin-left: 8px;
}

.filter-row {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.filter-select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-sm);
  min-width: 120px;
  transition: border-color var(--transition-fast);
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  text-align: center;
  color: var(--text-secondary);
}

.loading-spinner,
.empty-icon {
  width: 64px;
  height: 64px;
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
}

.loading-spinner svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.empty-state h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-action {
  margin-top: var(--spacing-lg);
}

.lessons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.lesson-card {
  transition: all var(--transition-normal);
}

.lesson-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.lesson-meta {
  display: flex;
  gap: var(--spacing-sm);
}

.lesson-subject,
.lesson-grade {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.lesson-subject {
  background-color: var(--primary-100);
  color: var(--primary-700);
}

.lesson-grade {
  background-color: var(--secondary-color);
  color: white;
}

.lesson-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.action-btn {
  background: none;
  border: none;
  padding: var(--spacing-xs);
  border-radius: var(--radius-md);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.action-btn:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.action-btn-danger:hover {
  background-color: var(--error-color);
  color: white;
}

.action-btn svg {
  width: 16px;
  height: 16px;
}

.lesson-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.lesson-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
  line-height: 1.4;
}

.lesson-description {
  color: var(--text-secondary);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.lesson-stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-top: auto;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-tertiary);
  font-size: var(--text-sm);
}

.stat-item svg {
  width: 16px;
  height: 16px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xl);
}

.page-numbers {
  display: flex;
  gap: var(--spacing-xs);
}

.page-btn {
  background: none;
  border: 1px solid var(--border-color);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  color: var(--text-primary);
  transition: all var(--transition-fast);
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-btn:hover {
  background-color: var(--primary-50);
  border-color: var(--primary-color);
}

.page-btn.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.delete-content {
  text-align: center;
  padding: var(--spacing-lg) 0;
}

.delete-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-lg);
  color: var(--error-color);
}

.delete-warning {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-top: var(--spacing-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filters-content {
    padding: var(--spacing-md);
  }
  
  .filter-row {
    flex-direction: column;
  }
  
  .filter-select {
    min-width: auto;
  }
  
  .lessons-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .pagination {
    flex-wrap: wrap;
  }
  
  .page-numbers {
    order: -1;
    width: 100%;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
  }
}
</style>
