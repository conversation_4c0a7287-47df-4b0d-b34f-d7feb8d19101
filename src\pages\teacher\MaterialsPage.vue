<template>
  <div class="materials-page">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2 class="page-title">相关资料</h2>
          <p class="page-description">管理您的教学资料，支持多种格式文件的上传、预览和分类</p>
        </div>
        <div class="header-right">
          <BaseButton
            variant="primary"
            size="lg"
            @click="triggerFileUpload"
          >
            <svg slot="icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M17 8L12 3L7 8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 3V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            上传文件
          </BaseButton>
        </div>
      </div>
    </div>

    <!-- 文件上传区域 -->
    <div class="upload-section">
      <BaseCard class="upload-card">
        <div 
          class="upload-area"
          :class="{ 'upload-area-dragover': isDragOver }"
          @drop="handleDrop"
          @dragover.prevent="handleDragOver"
          @dragleave="handleDragLeave"
          @click="triggerFileUpload"
        >
          <input
            ref="fileInputRef"
            type="file"
            multiple
            accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.jpg,.jpeg,.png,.gif,.mp4,.mp3"
            @change="handleFileSelect"
            style="display: none"
          />
          
          <div class="upload-content">
            <div class="upload-icon">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M16 13H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M16 17H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M10 9H9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <h3>拖拽文件到此处或点击上传</h3>
            <p>支持 PDF、Word、PPT、Excel、图片、音视频等格式</p>
            <p class="upload-limit">单个文件最大 50MB，最多同时上传 10 个文件</p>
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filters-section">
      <BaseCard class="filters-card">
        <div class="filters-content">
          <div class="search-row">
            <BaseInput
              v-model="searchQuery"
              placeholder="搜索文件名..."
              size="lg"
              clearable
              @input="handleSearch"
            >
              <template #prefix-icon>
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                  <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </template>
            </BaseInput>
          </div>
          
          <div class="filter-row">
            <select v-model="filters.category" class="filter-select" @change="handleFilter">
              <option value="">全部分类</option>
              <option value="document">文档</option>
              <option value="presentation">演示文稿</option>
              <option value="spreadsheet">表格</option>
              <option value="image">图片</option>
              <option value="video">视频</option>
              <option value="audio">音频</option>
              <option value="other">其他</option>
            </select>
            
            <select v-model="filters.sortBy" class="filter-select" @change="handleFilter">
              <option value="uploadTime">上传时间</option>
              <option value="name">文件名</option>
              <option value="size">文件大小</option>
              <option value="type">文件类型</option>
            </select>
            
            <select v-model="filters.sortOrder" class="filter-select" @change="handleFilter">
              <option value="desc">降序</option>
              <option value="asc">升序</option>
            </select>
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- 文件列表 -->
    <div class="files-section">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
            <path d="M12 3C16.9706 3 21 7.02944 21 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </div>
        <p>加载中...</p>
      </div>

      <div v-else-if="filteredFiles.length === 0" class="empty-state">
        <div class="empty-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V9L13 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M13 2V9H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h3>暂无文件</h3>
        <p>您还没有上传任何文件，点击上方按钮开始上传吧！</p>
      </div>

      <div v-else class="files-grid">
        <BaseCard
          v-for="file in paginatedFiles"
          :key="file.id"
          class="file-card"
          hoverable
        >
          <div class="file-content">
            <div class="file-icon">
              <component :is="getFileIcon(file.type)" />
            </div>
            
            <div class="file-info">
              <h4 class="file-name" :title="file.name">{{ file.name }}</h4>
              <div class="file-meta">
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
                <span class="file-date">{{ formatDate(file.uploadTime) }}</span>
              </div>
              <div class="file-category">
                <span class="category-tag" :class="`category-${file.category}`">
                  {{ getCategoryName(file.category) }}
                </span>
              </div>
            </div>
            
            <div class="file-actions">
              <button class="action-btn" @click="previewFile(file)" title="预览">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                </svg>
              </button>
              <button class="action-btn" @click="downloadFile(file)" title="下载">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
              <button class="action-btn action-btn-danger" @click="deleteFile(file)" title="删除">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </button>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="pagination">
        <BaseButton
          variant="outline"
          :disabled="currentPage === 1"
          @click="goToPage(currentPage - 1)"
        >
          上一页
        </BaseButton>
        
        <div class="page-numbers">
          <button
            v-for="page in visiblePages"
            :key="page"
            class="page-btn"
            :class="{ active: page === currentPage }"
            @click="goToPage(page)"
          >
            {{ page }}
          </button>
        </div>
        
        <BaseButton
          variant="outline"
          :disabled="currentPage === totalPages"
          @click="goToPage(currentPage + 1)"
        >
          下一页
        </BaseButton>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <BaseModal
      v-model="showDeleteModal"
      title="确认删除"
      size="sm"
    >
      <div class="delete-content">
        <div class="delete-icon">
          <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M15 9L9 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M9 9L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <p>确定要删除文件"{{ selectedFile?.name }}"吗？</p>
        <p class="delete-warning">此操作不可撤销，请谨慎操作。</p>
      </div>
      <template #footer>
        <BaseButton
          variant="outline"
          @click="showDeleteModal = false"
        >
          取消
        </BaseButton>
        <BaseButton
          variant="danger"
          @click="confirmDelete"
          :loading="isDeleting"
        >
          确认删除
        </BaseButton>
      </template>
    </BaseModal>

    <!-- 文件预览模态框 -->
    <BaseModal
      v-model="showPreviewModal"
      :title="previewFile?.name"
      size="xl"
    >
      <div v-if="selectedFile" class="file-preview">
        <div v-if="isImageFile(selectedFile)" class="image-preview">
          <img :src="selectedFile.url" :alt="selectedFile.name" />
        </div>
        <div v-else-if="isVideoFile(selectedFile)" class="video-preview">
          <video :src="selectedFile.url" controls></video>
        </div>
        <div v-else-if="isAudioFile(selectedFile)" class="audio-preview">
          <audio :src="selectedFile.url" controls></audio>
        </div>
        <div v-else class="document-preview">
          <div class="preview-placeholder">
            <component :is="getFileIcon(selectedFile.type)" />
            <h4>{{ selectedFile.name }}</h4>
            <p>此文件类型暂不支持在线预览</p>
            <BaseButton
              variant="primary"
              @click="downloadFile(selectedFile)"
            >
              下载文件
            </BaseButton>
          </div>
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import BaseInput from '@/components/common/BaseInput.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseModal from '@/components/common/BaseModal.vue'

export default {
  name: 'MaterialsPage',
  components: {
    BaseInput,
    BaseButton,
    BaseCard,
    BaseModal
  },
  setup() {
    const files = ref([])
    const loading = ref(true)
    const searchQuery = ref('')
    const currentPage = ref(1)
    const pageSize = 12
    const isDragOver = ref(false)
    const showDeleteModal = ref(false)
    const showPreviewModal = ref(false)
    const selectedFile = ref(null)
    const isDeleting = ref(false)
    const fileInputRef = ref(null)

    const filters = reactive({
      category: '',
      sortBy: 'uploadTime',
      sortOrder: 'desc'
    })

    // 模拟数据
    const mockFiles = [
      {
        id: 1,
        name: '小学数学教案.docx',
        type: 'docx',
        category: 'document',
        size: 1024 * 1024 * 2.5, // 2.5MB
        uploadTime: new Date('2024-01-15'),
        url: '/mock/files/lesson-plan.docx'
      },
      {
        id: 2,
        name: '课堂演示.pptx',
        type: 'pptx',
        category: 'presentation',
        size: 1024 * 1024 * 8.2, // 8.2MB
        uploadTime: new Date('2024-01-14'),
        url: '/mock/files/presentation.pptx'
      },
      {
        id: 3,
        name: '学生成绩表.xlsx',
        type: 'xlsx',
        category: 'spreadsheet',
        size: 1024 * 512, // 512KB
        uploadTime: new Date('2024-01-13'),
        url: '/mock/files/grades.xlsx'
      },
      {
        id: 4,
        name: '教学图片.jpg',
        type: 'jpg',
        category: 'image',
        size: 1024 * 1024 * 1.2, // 1.2MB
        uploadTime: new Date('2024-01-12'),
        url: 'https://via.placeholder.com/800x600/4f46e5/ffffff?text=Teaching+Image'
      },
      {
        id: 5,
        name: '课程视频.mp4',
        type: 'mp4',
        category: 'video',
        size: 1024 * 1024 * 25.6, // 25.6MB
        uploadTime: new Date('2024-01-11'),
        url: '/mock/files/course-video.mp4'
      }
    ]

    const filteredFiles = computed(() => {
      let result = [...files.value]

      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        result = result.filter(file =>
          file.name.toLowerCase().includes(query)
        )
      }

      // 分类过滤
      if (filters.category) {
        result = result.filter(file => file.category === filters.category)
      }

      // 排序
      result.sort((a, b) => {
        const aValue = a[filters.sortBy]
        const bValue = b[filters.sortBy]

        if (filters.sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1
        } else {
          return aValue < bValue ? 1 : -1
        }
      })

      return result
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredFiles.value.length / pageSize)
    })

    const paginatedFiles = computed(() => {
      const start = (currentPage.value - 1) * pageSize
      const end = start + pageSize
      return filteredFiles.value.slice(start, end)
    })

    const visiblePages = computed(() => {
      const pages = []
      const total = totalPages.value
      const current = currentPage.value

      if (total <= 7) {
        for (let i = 1; i <= total; i++) {
          pages.push(i)
        }
      } else {
        if (current <= 4) {
          for (let i = 1; i <= 5; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        } else if (current >= total - 3) {
          pages.push(1)
          pages.push('...')
          for (let i = total - 4; i <= total; i++) {
            pages.push(i)
          }
        } else {
          pages.push(1)
          pages.push('...')
          for (let i = current - 1; i <= current + 1; i++) {
            pages.push(i)
          }
          pages.push('...')
          pages.push(total)
        }
      }

      return pages
    })

    const loadFiles = async () => {
      loading.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        files.value = mockFiles
      } catch (error) {
        console.error('加载文件列表失败:', error)
      } finally {
        loading.value = false
      }
    }

    const triggerFileUpload = () => {
      fileInputRef.value?.click()
    }

    const handleFileSelect = (event) => {
      const selectedFiles = Array.from(event.target.files)
      uploadFiles(selectedFiles)
    }

    const handleDrop = (event) => {
      event.preventDefault()
      isDragOver.value = false
      const droppedFiles = Array.from(event.dataTransfer.files)
      uploadFiles(droppedFiles)
    }

    const handleDragOver = (event) => {
      event.preventDefault()
      isDragOver.value = true
    }

    const handleDragLeave = () => {
      isDragOver.value = false
    }

    const uploadFiles = async (fileList) => {
      if (fileList.length === 0) return

      // 验证文件
      const validFiles = fileList.filter(file => {
        if (file.size > 50 * 1024 * 1024) { // 50MB
          alert(`文件 ${file.name} 超过 50MB 限制`)
          return false
        }
        return true
      })

      if (validFiles.length > 10) {
        alert('最多只能同时上传 10 个文件')
        return
      }

      // 模拟上传
      for (const file of validFiles) {
        const newFile = {
          id: Date.now() + Math.random(),
          name: file.name,
          type: file.name.split('.').pop().toLowerCase(),
          category: getFileCategory(file.type),
          size: file.size,
          uploadTime: new Date(),
          url: URL.createObjectURL(file)
        }
        files.value.unshift(newFile)
      }

      // 清空文件输入
      if (fileInputRef.value) {
        fileInputRef.value.value = ''
      }
    }

    const getFileCategory = (mimeType) => {
      if (mimeType.startsWith('image/')) return 'image'
      if (mimeType.startsWith('video/')) return 'video'
      if (mimeType.startsWith('audio/')) return 'audio'
      if (mimeType.includes('pdf')) return 'document'
      if (mimeType.includes('word') || mimeType.includes('document')) return 'document'
      if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return 'presentation'
      if (mimeType.includes('sheet') || mimeType.includes('excel')) return 'spreadsheet'
      return 'other'
    }

    const getCategoryName = (category) => {
      const names = {
        document: '文档',
        presentation: '演示文稿',
        spreadsheet: '表格',
        image: '图片',
        video: '视频',
        audio: '音频',
        other: '其他'
      }
      return names[category] || '未知'
    }

    const getFileIcon = (fileType) => {
      // 返回文件图标组件名或SVG
      const iconMap = {
        pdf: 'FileTextIcon',
        doc: 'FileTextIcon',
        docx: 'FileTextIcon',
        ppt: 'PresentationIcon',
        pptx: 'PresentationIcon',
        xls: 'SpreadsheetIcon',
        xlsx: 'SpreadsheetIcon',
        jpg: 'ImageIcon',
        jpeg: 'ImageIcon',
        png: 'ImageIcon',
        gif: 'ImageIcon',
        mp4: 'VideoIcon',
        mp3: 'AudioIcon',
        default: 'FileIcon'
      }

      // 这里返回SVG字符串，实际项目中可以使用图标组件
      return 'svg'
    }

    const isImageFile = (file) => {
      return ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(file.type.toLowerCase())
    }

    const isVideoFile = (file) => {
      return ['mp4', 'webm', 'ogg'].includes(file.type.toLowerCase())
    }

    const isAudioFile = (file) => {
      return ['mp3', 'wav', 'ogg'].includes(file.type.toLowerCase())
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const formatDate = (date) => {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    const handleSearch = () => {
      currentPage.value = 1
    }

    const handleFilter = () => {
      currentPage.value = 1
    }

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
      }
    }

    const previewFile = (file) => {
      selectedFile.value = file
      showPreviewModal.value = true
    }

    const downloadFile = (file) => {
      // 创建下载链接
      const link = document.createElement('a')
      link.href = file.url
      link.download = file.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }

    const deleteFile = (file) => {
      selectedFile.value = file
      showDeleteModal.value = true
    }

    const confirmDelete = async () => {
      if (!selectedFile.value) return

      isDeleting.value = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        const index = files.value.findIndex(f => f.id === selectedFile.value.id)
        if (index > -1) {
          files.value.splice(index, 1)
        }

        showDeleteModal.value = false
        selectedFile.value = null
      } catch (error) {
        console.error('删除文件失败:', error)
      } finally {
        isDeleting.value = false
      }
    }

    onMounted(() => {
      loadFiles()
    })

    return {
      files,
      loading,
      searchQuery,
      filters,
      currentPage,
      totalPages,
      filteredFiles,
      paginatedFiles,
      visiblePages,
      isDragOver,
      showDeleteModal,
      showPreviewModal,
      selectedFile,
      isDeleting,
      fileInputRef,
      triggerFileUpload,
      handleFileSelect,
      handleDrop,
      handleDragOver,
      handleDragLeave,
      getCategoryName,
      getFileIcon,
      isImageFile,
      isVideoFile,
      isAudioFile,
      formatFileSize,
      formatDate,
      handleSearch,
      handleFilter,
      goToPage,
      previewFile,
      downloadFile,
      deleteFile,
      confirmDelete
    }
  }
}
</script>

<style scoped>
.materials-page {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-description {
  font-size: var(--text-lg);
  color: var(--text-secondary);
}

.upload-section {
  margin-bottom: var(--spacing-xl);
}

.upload-card {
  padding: 0;
}

.upload-area {
  padding: var(--spacing-2xl);
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  background-color: var(--bg-secondary);
}

.upload-area:hover,
.upload-area-dragover {
  border-color: var(--primary-color);
  background-color: var(--primary-50);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.upload-icon {
  width: 64px;
  height: 64px;
  color: var(--primary-color);
}

.upload-content h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.upload-content p {
  color: var(--text-secondary);
  margin: 0;
}

.upload-limit {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

.filters-section {
  margin-bottom: var(--spacing-xl);
}

.filters-card {
  padding: 0;
}

.filters-content {
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.search-row {
  display: flex;
}

.filter-row {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.filter-select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-sm);
  min-width: 120px;
  transition: border-color var(--transition-fast);
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  text-align: center;
  color: var(--text-secondary);
}

.loading-spinner,
.empty-icon {
  width: 64px;
  height: 64px;
  margin-bottom: var(--spacing-lg);
  color: var(--primary-color);
}

.loading-spinner svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.empty-state h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.file-card {
  transition: all var(--transition-normal);
}

.file-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
}

.file-icon {
  width: 48px;
  height: 48px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-100);
  color: var(--primary-600);
  border-radius: var(--radius-lg);
}

.file-icon svg {
  width: 24px;
  height: 24px;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.file-size,
.file-date {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.file-category {
  display: flex;
}

.category-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.category-document {
  background-color: var(--primary-100);
  color: var(--primary-700);
}

.category-presentation {
  background-color: #fef3c7;
  color: #d97706;
}

.category-spreadsheet {
  background-color: #d1fae5;
  color: #059669;
}

.category-image {
  background-color: #fce7f3;
  color: #be185d;
}

.category-video {
  background-color: #e0e7ff;
  color: #4338ca;
}

.category-audio {
  background-color: #f3e8ff;
  color: #7c3aed;
}

.category-other {
  background-color: var(--gray-100);
  color: var(--gray-700);
}

.file-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.action-btn {
  background: none;
  border: none;
  padding: var(--spacing-xs);
  border-radius: var(--radius-md);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.action-btn:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.action-btn-danger:hover {
  background-color: var(--error-color);
  color: white;
}

.action-btn svg {
  width: 16px;
  height: 16px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xl);
}

.page-numbers {
  display: flex;
  gap: var(--spacing-xs);
}

.page-btn {
  background: none;
  border: 1px solid var(--border-color);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  color: var(--text-primary);
  transition: all var(--transition-fast);
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-btn:hover {
  background-color: var(--primary-50);
  border-color: var(--primary-color);
}

.page-btn.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.delete-content {
  text-align: center;
  padding: var(--spacing-lg) 0;
}

.delete-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-lg);
  color: var(--error-color);
}

.delete-warning {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-top: var(--spacing-sm);
}

.file-preview {
  padding: var(--spacing-lg) 0;
}

.image-preview img {
  max-width: 100%;
  height: auto;
  border-radius: var(--radius-md);
}

.video-preview video,
.audio-preview audio {
  width: 100%;
  max-width: 100%;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-2xl);
  text-align: center;
}

.preview-placeholder svg {
  width: 64px;
  height: 64px;
  color: var(--text-secondary);
}

.preview-placeholder h4 {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin: 0;
}

.preview-placeholder p {
  color: var(--text-secondary);
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .filters-content {
    padding: var(--spacing-md);
  }

  .filter-row {
    flex-direction: column;
  }

  .filter-select {
    min-width: auto;
  }

  .files-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .file-content {
    padding: var(--spacing-md);
  }

  .file-actions {
    flex-direction: row;
  }

  .pagination {
    flex-wrap: wrap;
  }

  .page-numbers {
    order: -1;
    width: 100%;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
  }

  .upload-area {
    padding: var(--spacing-xl);
  }

  .upload-content h3 {
    font-size: var(--text-lg);
  }
}
</style>
