<template>
  <div class="ppt-generator-page">
    <div class="page-header">
      <h2 class="page-title">PPT生成</h2>
      <p class="page-description">通过AI智能对话，快速生成精美的PPT演示文稿</p>
    </div>

    <div class="generator-container">
      <!-- 对话历史区域 -->
      <div class="chat-history" ref="chatHistoryRef">
        <div v-if="messages.length === 0" class="empty-state">
          <div class="empty-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="3" y="4" width="18" height="15" rx="2" stroke="currentColor" stroke-width="2"/>
              <path d="M7 15L10 12L13 15L17 11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <h3>开始生成您的PPT</h3>
          <p>请输入您想要生成PPT的主题和要求，AI将为您创建专业的演示文稿</p>
          <div class="example-topics">
            <h4>示例主题：</h4>
            <div class="topic-tags">
              <span class="topic-tag" @click="selectTopic('小学数学：分数的认识 - 课堂教学PPT')">小学数学：分数的认识</span>
              <span class="topic-tag" @click="selectTopic('初中语文：古诗词鉴赏 - 互动教学PPT')">初中语文：古诗词鉴赏</span>
              <span class="topic-tag" @click="selectTopic('高中物理：牛顿运动定律 - 实验演示PPT')">高中物理：牛顿运动定律</span>
              <span class="topic-tag" @click="selectTopic('班会主题：团队合作的重要性')">班会：团队合作</span>
            </div>
          </div>
          <div class="ppt-features">
            <h4>PPT生成特色：</h4>
            <div class="feature-list">
              <div class="feature-item">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22 12H18L15 21L9 3L6 12H2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>智能布局设计</span>
              </div>
              <div class="feature-item">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                  <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>多种主题模板</span>
              </div>
              <div class="feature-item">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M14.7 6.3C15.0314 6.63137 15.2 7.08579 15.2 7.56C15.2 8.03421 15.0314 8.48863 14.7 8.82L11 12.52L7.3 8.82C6.96863 8.48863 6.8 8.03421 6.8 7.56C6.8 7.08579 6.96863 6.63137 7.3 6.3C7.63137 5.96863 8.08579 5.8 8.56 5.8C9.03421 5.8 9.48863 5.96863 9.82 6.3L11 7.48L12.18 6.3C12.5114 5.96863 12.9658 5.8 13.44 5.8C13.9142 5.8 14.3686 5.96863 14.7 6.3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M14.7 15.3C15.0314 15.6314 15.2 16.0858 15.2 16.56C15.2 17.0342 15.0314 17.4886 14.7 17.82L11 21.52L7.3 17.82C6.96863 17.4886 6.8 17.0342 6.8 16.56C6.8 16.0858 6.96863 15.6314 7.3 15.3C7.63137 14.9686 8.08579 14.8 8.56 14.8C9.03421 14.8 9.48863 14.9686 9.82 15.3L11 16.48L12.18 15.3C12.5114 14.9686 12.9658 14.8 13.44 14.8C13.9142 14.8 14.3686 14.9686 14.7 15.3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>动画效果丰富</span>
              </div>
              <div class="feature-item">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span>一键导出下载</span>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="messages">
          <div
            v-for="(message, index) in messages"
            :key="index"
            class="message"
            :class="{ 'message-user': message.type === 'user', 'message-ai': message.type === 'ai' }"
          >
            <div class="message-avatar">
              <svg v-if="message.type === 'user'" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
              </svg>
              <svg v-else viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="3" y="4" width="18" height="15" rx="2" stroke="currentColor" stroke-width="2"/>
                <path d="M7 15L10 12L13 15L17 11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <div class="message-content">
              <div class="message-text" v-html="formatMessage(message.content)"></div>
              <div v-if="message.type === 'ai' && message.pptPreview" class="ppt-preview">
                <div class="preview-header">
                  <h4>PPT预览</h4>
                  <BaseButton
                    variant="primary"
                    size="sm"
                    @click="downloadPPT(message.pptPreview)"
                  >
                    下载PPT
                  </BaseButton>
                </div>
                <div class="slides-preview">
                  <div
                    v-for="(slide, slideIndex) in message.pptPreview.slides"
                    :key="slideIndex"
                    class="slide-thumbnail"
                    @click="viewSlide(slide, slideIndex)"
                  >
                    <div class="slide-number">{{ slideIndex + 1 }}</div>
                    <div class="slide-content">
                      <h5>{{ slide.title }}</h5>
                      <p>{{ slide.content.substring(0, 50) }}...</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoading" class="message message-ai">
            <div class="message-avatar">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="3" y="4" width="18" height="15" rx="2" stroke="currentColor" stroke-width="2"/>
                <path d="M7 15L10 12L13 15L17 11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>
            <div class="message-content">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
              <div class="loading-text">正在生成PPT，请稍候...</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <div class="input-container">
          <textarea
            v-model="inputMessage"
            placeholder="请详细描述您想要生成的PPT内容，包括主题、目标受众、页数要求等..."
            class="message-input"
            rows="3"
            @keydown.enter.exact.prevent="sendMessage"
            @keydown.enter.shift.exact="addNewLine"
            :disabled="isLoading"
          ></textarea>
          <button
            class="send-button"
            @click="sendMessage"
            :disabled="!inputMessage.trim() || isLoading"
          >
            <svg v-if="!isLoading" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22 2L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg v-else class="loading-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
              <path d="M12 3C16.9706 3 21 7.02944 21 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
        <div class="input-tips">
          <span class="tip">按 Enter 发送，Shift + Enter 换行</span>
          <span class="tip">建议详细描述PPT需求以获得更好的生成效果</span>
        </div>
      </div>
    </div>

    <!-- 幻灯片预览模态框 -->
    <BaseModal
      v-model="showSlideModal"
      title="幻灯片预览"
      size="lg"
    >
      <div v-if="currentSlide" class="slide-detail">
        <div class="slide-header">
          <h3>第 {{ currentSlideIndex + 1 }} 页：{{ currentSlide.title }}</h3>
        </div>
        <div class="slide-body">
          <div class="slide-content-detail" v-html="formatSlideContent(currentSlide.content)"></div>
        </div>
      </div>
      <template #footer>
        <BaseButton
          variant="outline"
          @click="showSlideModal = false"
        >
          关闭
        </BaseButton>
        <BaseButton
          variant="primary"
          @click="editSlide"
        >
          编辑此页
        </BaseButton>
      </template>
    </BaseModal>
  </div>
</template>

<script>
import { ref, nextTick } from 'vue'
import BaseButton from '@/components/common/BaseButton.vue'
import BaseModal from '@/components/common/BaseModal.vue'

export default {
  name: 'PPTGeneratorPage',
  components: {
    BaseButton,
    BaseModal
  },
  setup() {
    const messages = ref([])
    const inputMessage = ref('')
    const isLoading = ref(false)
    const chatHistoryRef = ref(null)
    const showSlideModal = ref(false)
    const currentSlide = ref(null)
    const currentSlideIndex = ref(0)

    const selectTopic = (topic) => {
      inputMessage.value = topic
    }

    const sendMessage = async () => {
      if (!inputMessage.value.trim() || isLoading.value) return

      const userMessage = {
        type: 'user',
        content: inputMessage.value.trim(),
        timestamp: new Date()
      }

      messages.value.push(userMessage)
      const currentInput = inputMessage.value.trim()
      inputMessage.value = ''
      isLoading.value = true

      await scrollToBottom()

      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 3000))

        const aiResponse = {
          type: 'ai',
          content: generateMockPPTResponse(currentInput),
          timestamp: new Date(),
          pptPreview: generateMockPPTPreview(currentInput)
        }

        messages.value.push(aiResponse)
      } catch (error) {
        console.error('生成PPT失败:', error)
        const errorResponse = {
          type: 'ai',
          content: '抱歉，生成PPT时出现了错误，请稍后重试。',
          timestamp: new Date()
        }
        messages.value.push(errorResponse)
      } finally {
        isLoading.value = false
        await scrollToBottom()
      }
    }

    const generateMockPPTResponse = (input) => {
      return `
        <h3>PPT生成完成！</h3>
        <p>根据您的需求"${input}"，我已经为您生成了一份专业的PPT演示文稿。</p>

        <h4>PPT特色：</h4>
        <ul>
          <li><strong>专业设计：</strong>采用现代化的设计风格，视觉效果佳</li>
          <li><strong>内容丰富：</strong>包含完整的教学流程和重点内容</li>
          <li><strong>互动元素：</strong>添加了适合课堂互动的元素</li>
          <li><strong>易于编辑：</strong>支持进一步的个性化修改</li>
        </ul>

        <p>您可以预览各个幻灯片，也可以直接下载使用。如需修改，请告诉我具体的调整要求。</p>
      `
    }

    const generateMockPPTPreview = (input) => {
      const slides = [
        {
          title: '课程标题页',
          content: `${input}\n\n授课教师：张老师\n授课时间：${new Date().toLocaleDateString()}\n适用年级：根据需求调整`
        },
        {
          title: '学习目标',
          content: '1. 知识目标：掌握核心概念和原理\n2. 能力目标：培养分析和解决问题的能力\n3. 情感目标：激发学习兴趣和探索精神'
        },
        {
          title: '课程大纲',
          content: '• 导入环节\n• 新知识讲解\n• 实践练习\n• 总结回顾\n• 课后作业'
        },
        {
          title: '重点内容讲解',
          content: '详细讲解课程的核心知识点，配合图表和实例说明，帮助学生更好地理解和掌握。'
        },
        {
          title: '互动练习',
          content: '设计针对性的练习题目，让学生在实践中巩固所学知识，提高应用能力。'
        },
        {
          title: '课程总结',
          content: '回顾本节课的主要内容，强调重点难点，为下节课做好铺垫。'
        }
      ]

      return {
        title: input,
        slideCount: slides.length,
        theme: 'professional',
        slides: slides
      }
    }

    const formatMessage = (content) => {
      return content.replace(/\n/g, '<br>')
    }

    const formatSlideContent = (content) => {
      return content.replace(/\n/g, '<br>').replace(/•/g, '&bull;')
    }

    const formatTime = (timestamp) => {
      return timestamp.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const scrollToBottom = async () => {
      await nextTick()
      if (chatHistoryRef.value) {
        chatHistoryRef.value.scrollTop = chatHistoryRef.value.scrollHeight
      }
    }

    const addNewLine = () => {
      inputMessage.value += '\n'
    }

    const viewSlide = (slide, index) => {
      currentSlide.value = slide
      currentSlideIndex.value = index
      showSlideModal.value = true
    }

    const editSlide = () => {
      console.log('编辑幻灯片:', currentSlide.value)
      showSlideModal.value = false
      // 这里可以实现幻灯片编辑功能
    }

    const downloadPPT = (pptPreview) => {
      console.log('下载PPT:', pptPreview)
      // 这里可以实现PPT下载功能
      alert('PPT下载功能将在后续版本中实现')
    }

    return {
      messages,
      inputMessage,
      isLoading,
      chatHistoryRef,
      showSlideModal,
      currentSlide,
      currentSlideIndex,
      selectTopic,
      sendMessage,
      formatMessage,
      formatSlideContent,
      formatTime,
      addNewLine,
      viewSlide,
      editSlide,
      downloadPPT
    }
  }
}
</script>

<style scoped>
.ppt-generator-page {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: var(--spacing-xl);
}

.page-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-description {
  font-size: var(--text-lg);
  color: var(--text-secondary);
}

.generator-container {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  height: 70vh;
  display: flex;
  flex-direction: column;
}

.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  width: 80px;
  height: 80px;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-state p {
  font-size: var(--text-base);
  margin-bottom: var(--spacing-xl);
}

.example-topics,
.ppt-features {
  margin-bottom: var(--spacing-lg);
}

.example-topics h4,
.ppt-features h4 {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.topic-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
}

.topic-tag {
  background-color: var(--primary-50);
  color: var(--primary-700);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.topic-tag:hover {
  background-color: var(--primary-100);
  transform: translateY(-1px);
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.feature-item svg {
  width: 20px;
  height: 20px;
  color: var(--primary-color);
  flex-shrink: 0;
}

.messages {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.message {
  display: flex;
  gap: var(--spacing-sm);
  align-items: flex-start;
}

.message-user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.message-user .message-avatar {
  background-color: var(--primary-100);
  color: var(--primary-600);
}

.message-ai .message-avatar {
  background-color: var(--secondary-color);
  color: white;
}

.message-avatar svg {
  width: 20px;
  height: 20px;
}

.message-content {
  max-width: 75%;
  background-color: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  position: relative;
}

.message-user .message-content {
  background-color: var(--primary-500);
  color: white;
}

.message-text {
  line-height: 1.6;
  word-wrap: break-word;
}

.message-text h3 {
  margin-bottom: var(--spacing-sm);
  color: inherit;
}

.message-text h4 {
  margin: var(--spacing-md) 0 var(--spacing-xs) 0;
  color: inherit;
}

.message-text ul, .message-text ol {
  margin: var(--spacing-sm) 0;
  padding-left: var(--spacing-lg);
}

.message-text li {
  margin-bottom: var(--spacing-xs);
}

.ppt-preview {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.preview-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.slides-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-sm);
}

.slide-thumbnail {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.slide-thumbnail:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.slide-number {
  position: absolute;
  top: var(--spacing-xs);
  right: var(--spacing-xs);
  background-color: var(--primary-color);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
}

.slide-content h5 {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.slide-content p {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  line-height: 1.4;
}

.message-time {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
}

.message-user .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--text-secondary);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-text {
  margin-top: var(--spacing-sm);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.input-area {
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-lg);
}

.input-container {
  display: flex;
  gap: var(--spacing-sm);
  align-items: flex-end;
}

.message-input {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  font-size: var(--text-base);
  line-height: 1.5;
  resize: none;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-fast);
}

.message-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.send-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px;
  height: 48px;
}

.send-button:hover:not(:disabled) {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

.send-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.send-button svg {
  width: 20px;
  height: 20px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.input-tips {
  margin-top: var(--spacing-sm);
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.tip {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

.slide-detail {
  padding: var(--spacing-lg) 0;
}

.slide-header {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.slide-header h3 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.slide-body {
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  min-height: 300px;
}

.slide-content-detail {
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .generator-container {
    height: 60vh;
  }

  .message-content {
    max-width: 85%;
  }

  .topic-tags {
    flex-direction: column;
    align-items: center;
  }

  .feature-list {
    grid-template-columns: 1fr;
  }

  .slides-preview {
    grid-template-columns: 1fr;
  }

  .input-tips {
    flex-direction: column;
    text-align: center;
  }

  .preview-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
}
</style>